#!/usr/bin/env python3
"""
Sample Data Producer

This script generates sample data and sends it to Kafka topics
for testing the Bronze-Silver-Gold pipeline.
"""

import json
import random
import time
from datetime import datetime, timezone
from typing import Dict, Any
from kafka import KafkaProducer
import uuid


class SampleDataProducer:
    """Produces sample data for testing the ELT pipeline."""
    
    def __init__(self, bootstrap_servers: str = "localhost:19092,localhost:19094,localhost:19096"):
        """Initialize the producer."""
        self.producer = KafkaProducer(
            bootstrap_servers=bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            key_serializer=lambda k: k.encode('utf-8') if k else None,
            acks='all',
            retries=3,
            compression_type='gzip'
        )
        
        # Sample data templates
        self.user_ids = [f"user_{i:04d}" for i in range(1, 1001)]  # 1000 users
        self.event_types = ["login", "logout", "page_view", "click", "purchase", "search"]
        self.pages = ["/home", "/products", "/cart", "/checkout", "/profile", "/help"]
        self.products = [f"product_{i:03d}" for i in range(1, 101)]  # 100 products
        self.currencies = ["USD", "EUR", "GBP", "JPY"]
        self.transaction_types = ["purchase", "refund", "fee", "transfer"]
        
    def generate_user_event(self) -> Dict[str, Any]:
        """Generate a sample user event."""
        user_id = random.choice(self.user_ids)
        event_type = random.choice(self.event_types)
        
        event = {
            "user_id": user_id,
            "session_id": f"session_{random.randint(1000, 9999)}",
            "event_type": event_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "ip_address": f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}",
            "user_agent": random.choice([
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
            ])
        }
        
        # Add event-specific data
        if event_type == "page_view":
            event["page"] = random.choice(self.pages)
            event["referrer"] = random.choice(self.pages + [None])
        elif event_type == "click":
            event["element_id"] = f"btn_{random.randint(1, 100)}"
            event["page"] = random.choice(self.pages)
        elif event_type == "purchase":
            event["product_id"] = random.choice(self.products)
            event["amount"] = round(random.uniform(10.0, 500.0), 2)
            event["currency"] = random.choice(self.currencies)
        elif event_type == "search":
            event["query"] = random.choice(["laptop", "phone", "book", "shoes", "camera"])
            event["results_count"] = random.randint(0, 100)
        
        return event
    
    def generate_transaction_log(self) -> Dict[str, Any]:
        """Generate a sample transaction log."""
        transaction_id = str(uuid.uuid4())
        user_id = random.choice(self.user_ids)
        transaction_type = random.choice(self.transaction_types)
        
        transaction = {
            "transaction_id": transaction_id,
            "user_id": user_id,
            "transaction_type": transaction_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "amount": round(random.uniform(1.0, 1000.0), 2),
            "currency": random.choice(self.currencies),
            "status": random.choice(["completed", "pending", "failed"]),
            "payment_method": random.choice(["credit_card", "debit_card", "paypal", "bank_transfer"])
        }
        
        # Add transaction-specific data
        if transaction_type == "purchase":
            transaction["product_id"] = random.choice(self.products)
            transaction["quantity"] = random.randint(1, 5)
        elif transaction_type == "refund":
            transaction["original_transaction_id"] = str(uuid.uuid4())
            transaction["reason"] = random.choice(["defective", "not_as_described", "changed_mind"])
        
        return transaction
    
    def generate_system_metric(self) -> Dict[str, Any]:
        """Generate a sample system metric."""
        metric = {
            "metric_name": random.choice(["cpu_usage", "memory_usage", "disk_usage", "network_io"]),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "value": round(random.uniform(0.0, 100.0), 2),
            "unit": random.choice(["percent", "bytes", "requests_per_second"]),
            "host": f"server_{random.randint(1, 10):02d}",
            "service": random.choice(["web", "api", "database", "cache", "queue"]),
            "environment": random.choice(["production", "staging", "development"])
        }
        
        # Add metric-specific data
        if metric["metric_name"] == "cpu_usage":
            metric["cores"] = random.randint(2, 16)
        elif metric["metric_name"] == "memory_usage":
            metric["total_memory_gb"] = random.choice([8, 16, 32, 64])
        elif metric["metric_name"] == "disk_usage":
            metric["total_disk_gb"] = random.choice([100, 500, 1000, 2000])
        
        return metric
    
    def produce_user_events(self, count: int = 100, delay: float = 0.1):
        """Produce user events to Kafka."""
        print(f"Producing {count} user events...")
        
        for i in range(count):
            event = self.generate_user_event()
            key = event["user_id"]
            
            self.producer.send("user_events", key=key, value=event)
            
            if (i + 1) % 10 == 0:
                print(f"Produced {i + 1} user events")
            
            time.sleep(delay)
        
        self.producer.flush()
        print(f"Completed producing {count} user events")
    
    def produce_transaction_logs(self, count: int = 50, delay: float = 0.2):
        """Produce transaction logs to Kafka."""
        print(f"Producing {count} transaction logs...")
        
        for i in range(count):
            transaction = self.generate_transaction_log()
            key = transaction["transaction_id"]
            
            self.producer.send("transaction_logs", key=key, value=transaction)
            
            if (i + 1) % 10 == 0:
                print(f"Produced {i + 1} transaction logs")
            
            time.sleep(delay)
        
        self.producer.flush()
        print(f"Completed producing {count} transaction logs")
    
    def produce_system_metrics(self, count: int = 200, delay: float = 0.05):
        """Produce system metrics to Kafka."""
        print(f"Producing {count} system metrics...")
        
        for i in range(count):
            metric = self.generate_system_metric()
            key = f"{metric['host']}_{metric['service']}"
            
            self.producer.send("system_metrics", key=key, value=metric)
            
            if (i + 1) % 20 == 0:
                print(f"Produced {i + 1} system metrics")
            
            time.sleep(delay)
        
        self.producer.flush()
        print(f"Completed producing {count} system metrics")
    
    def produce_continuous(self, duration_minutes: int = 10):
        """Produce data continuously for testing."""
        print(f"Starting continuous data production for {duration_minutes} minutes...")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        while time.time() < end_time:
            # Produce different types of events with different frequencies
            
            # User events (most frequent)
            for _ in range(5):
                event = self.generate_user_event()
                self.producer.send("user_events", key=event["user_id"], value=event)
            
            # Transaction logs (medium frequency)
            if random.random() < 0.3:  # 30% chance
                transaction = self.generate_transaction_log()
                self.producer.send("transaction_logs", key=transaction["transaction_id"], value=transaction)
            
            # System metrics (high frequency)
            for _ in range(10):
                metric = self.generate_system_metric()
                key = f"{metric['host']}_{metric['service']}"
                self.producer.send("system_metrics", key=key, value=metric)
            
            time.sleep(1)  # 1 second between batches
        
        self.producer.flush()
        print("Continuous data production completed")
    
    def close(self):
        """Close the producer."""
        self.producer.close()


def main():
    """Main function for running the sample data producer."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Sample Data Producer for ELT Pipeline")
    parser.add_argument("--bootstrap-servers", default="localhost:19092,localhost:19094,localhost:19096",
                       help="Kafka bootstrap servers")
    parser.add_argument("--mode", choices=["batch", "continuous"], default="batch",
                       help="Production mode")
    parser.add_argument("--user-events", type=int, default=100,
                       help="Number of user events to produce (batch mode)")
    parser.add_argument("--transaction-logs", type=int, default=50,
                       help="Number of transaction logs to produce (batch mode)")
    parser.add_argument("--system-metrics", type=int, default=200,
                       help="Number of system metrics to produce (batch mode)")
    parser.add_argument("--duration", type=int, default=10,
                       help="Duration in minutes for continuous mode")
    
    args = parser.parse_args()
    
    producer = SampleDataProducer(args.bootstrap_servers)
    
    try:
        if args.mode == "batch":
            producer.produce_user_events(args.user_events)
            producer.produce_transaction_logs(args.transaction_logs)
            producer.produce_system_metrics(args.system_metrics)
        else:
            producer.produce_continuous(args.duration)
    
    finally:
        producer.close()


if __name__ == "__main__":
    main()
