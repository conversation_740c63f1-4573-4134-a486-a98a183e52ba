# Examples and Usage Guide

This directory contains examples and utilities for working with the Kafka → Daft → Iceberg ELT pipeline.

## Quick Start Guide

### 1. Infrastructure Setup

Start the required infrastructure using Docker Compose:

```bash
# Start Kafka, MinIO, Hive Metastore, and <PERSON>
docker-compose -f config/docker-compose-infrastructure.yml up -d

# Wait for services to be ready (about 2-3 minutes)
docker-compose -f config/docker-compose-infrastructure.yml logs -f
```

This will start:
- **Kafka Cluster**: 3 brokers on ports 19092, 19094, 19096
- **MinIO**: S3-compatible storage on port 9000 (UI on 9001)
- **Hive Metastore**: Iceberg catalog on port 9083
- **PostgreSQL**: Metastore database on port 5432
- **Ray Head Node**: Distributed computing on port 8265 (dashboard)
- **Kafka UI**: Monitoring on port 8080

### 2. Install Dependencies

```bash
# Install the extended requirements
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

### 3. Generate Sample Data

```bash
# Generate batch sample data
python examples/sample_data_producer.py --mode batch

# Or generate continuous data for 10 minutes
python examples/sample_data_producer.py --mode continuous --duration 10
```

### 4. Start Bronze Layer Processing

```bash
# Set environment variables
export WORKER_CONFIG_PATH=config/bronze_layer_config.json
export LOCAL_MODE=N
export RAY_HEAD_ADDRESS=ray://localhost:10001

# Start the Bronze layer consumer
uvicorn src.event_consumer_app:app --port 8000 --reload
```

### 5. Start ELT Pipeline Manager

```bash
# Start the ELT pipeline manager
uvicorn src.elt_manager_app:app --port 8001 --reload
```

### 6. Monitor and Manage

Access the web interfaces:
- **Kafka UI**: http://localhost:8080 - Monitor Kafka topics and messages
- **MinIO Console**: http://localhost:9001 - View stored data files (minioadmin/minioadmin)
- **Ray Dashboard**: http://localhost:8265 - Monitor Ray cluster and tasks
- **Bronze Consumer API**: http://localhost:8000/docs - Manage Kafka consumers
- **ELT Manager API**: http://localhost:8001/docs - Manage ELT pipelines

## Configuration Examples

### Bronze Layer Configuration

The Bronze layer processes raw Kafka data and stores it in Iceberg tables:

```json
{
  "consumer_name": "bronze_user_events",
  "topic_name": "user_events",
  "number_of_workers": 3,
  "sink_configs": {
    "transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer",
    "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"],
    "batch_size": 1000,
    "batch_timeout_seconds": 30,
    "table_name": "bronze_user_events",
    "database_name": "lakehouse",
    "iceberg_catalog": {
      "type": "hive",
      "uri": "thrift://localhost:9083",
      "warehouse": "s3://data-lake/warehouse"
    }
  }
}
```

### Silver Layer ELT Configuration

The Silver layer cleans and enriches Bronze data:

```json
{
  "pipeline_name": "user_events_silver",
  "pipeline_type": "silver",
  "source_table": "lakehouse.bronze_user_events",
  "target_table": "lakehouse.silver_user_events",
  "schedule": {
    "type": "interval",
    "interval_minutes": 15
  },
  "processing_config": {
    "data_quality_rules": {
      "user_id": {"allow_null": false, "type": "str"},
      "event_type": {"allowed_values": ["login", "logout", "page_view", "click", "purchase"]}
    },
    "transformations": [
      {
        "name": "parse_timestamp",
        "type": "datetime_parse",
        "source_column": "timestamp",
        "target_column": "event_timestamp"
      }
    ]
  }
}
```

### Gold Layer ELT Configuration

The Gold layer creates business-ready aggregations:

```json
{
  "pipeline_name": "user_events_gold_daily",
  "pipeline_type": "gold",
  "source_tables": ["lakehouse.silver_user_events"],
  "target_table": "lakehouse.gold_user_events_daily",
  "schedule": {
    "type": "cron",
    "cron_expression": "0 2 * * *"
  },
  "aggregations": [
    {
      "name": "daily_user_activity",
      "group_by": ["user_id", "_event_date"],
      "metrics": [
        {"name": "total_events", "type": "count", "column": "*"},
        {"name": "unique_sessions", "type": "count_distinct", "column": "session_id"}
      ]
    }
  ]
}
```

## API Usage Examples

### Managing Bronze Layer Consumers

```bash
# Start all consumers
curl -X POST "http://localhost:8000/manager/start-consumers" \
  -u admin:admin

# Check consumer status
curl -X GET "http://localhost:8000/manager/fetch-consumers" \
  -u admin:admin

# Stop all consumers
curl -X POST "http://localhost:8000/manager/stop-consumers" \
  -u admin:admin
```

### Managing ELT Pipelines

```bash
# Execute a specific Silver layer pipeline
curl -X POST "http://localhost:8001/elt/execute-pipeline" \
  -H "Content-Type: application/json" \
  -u admin:admin \
  -d '{"pipeline_name": "user_events_silver"}'

# Execute all pipelines
curl -X POST "http://localhost:8001/elt/execute-pipeline" \
  -H "Content-Type: application/json" \
  -u admin:admin \
  -d '{"execute_all": true}'

# Check pipeline status
curl -X GET "http://localhost:8001/elt/pipeline-status" \
  -u admin:admin

# Start/stop scheduler
curl -X POST "http://localhost:8001/elt/start-scheduler" \
  -u admin:admin
```

## Data Flow Architecture

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Kafka     │───▶│   Bronze     │───▶│   Silver    │
│   Topics    │    │   Layer      │    │   Layer     │
│             │    │  (Raw Data)  │    │ (Cleaned)   │
└─────────────┘    └──────────────┘    └─────────────┘
                                              │
                                              ▼
                                       ┌─────────────┐
                                       │    Gold     │
                                       │   Layer     │
                                       │(Aggregated) │
                                       └─────────────┘
```

### Bronze Layer
- **Purpose**: Store raw data with minimal transformation
- **Data**: Original Kafka messages + metadata (offsets, timestamps, etc.)
- **Format**: Parquet files in Iceberg tables
- **Partitioning**: By year/month for efficient querying
- **Retention**: Long-term storage (years)

### Silver Layer
- **Purpose**: Clean, validate, and enrich data
- **Data**: Deduplicated, validated, and enriched records
- **Transformations**: Data quality checks, type conversions, enrichment
- **Format**: Parquet files in Iceberg tables
- **Partitioning**: By date and business dimensions
- **Retention**: Medium-term storage (months to years)

### Gold Layer
- **Purpose**: Business-ready aggregated datasets
- **Data**: Pre-computed metrics, KPIs, and aggregations
- **Transformations**: Business logic, aggregations, calculations
- **Format**: Optimized Parquet files
- **Partitioning**: By business dimensions
- **Retention**: Optimized for query performance

## Monitoring and Troubleshooting

### Key Metrics to Monitor

1. **Kafka Consumer Lag**: Check in Kafka UI
2. **Ray Task Status**: Check in Ray Dashboard
3. **Iceberg Table Stats**: Query table metadata
4. **ELT Pipeline Success Rate**: Check API responses
5. **Data Quality Metrics**: Monitor validation failures

### Common Issues

1. **Consumer Lag**: Increase number of workers or batch size
2. **Memory Issues**: Adjust Ray worker memory allocation
3. **Schema Evolution**: Use Iceberg's schema evolution features
4. **Data Quality**: Review and adjust validation rules
5. **Performance**: Optimize partitioning and file sizes

### Logs and Debugging

```bash
# Check consumer logs
docker-compose -f config/docker-compose-infrastructure.yml logs kafka-consumer

# Check Ray logs
docker-compose -f config/docker-compose-infrastructure.yml logs ray-head

# Check application logs
tail -f logs/application.log
```

## Advanced Usage

### Custom Transformers

Create custom transformers by extending the base classes:

```python
from src.transformers.transformer import StreamTransformer

class MyCustomTransformer(StreamTransformer):
    def transform(self, consumer_record):
        # Your custom transformation logic
        pass
```

### Custom Stream Writers

Create custom stream writers for different destinations:

```python
from src.stream_writers.stream_writer import StreamWriter

class MyCustomWriter(StreamWriter):
    def write(self, streams):
        # Your custom write logic
        pass
```

### Scaling Considerations

1. **Horizontal Scaling**: Add more Ray worker nodes
2. **Kafka Partitioning**: Increase topic partitions for parallelism
3. **Batch Sizing**: Tune batch sizes for optimal throughput
4. **Resource Allocation**: Adjust CPU/memory per Ray actor
5. **Storage Optimization**: Configure Iceberg compaction settings
