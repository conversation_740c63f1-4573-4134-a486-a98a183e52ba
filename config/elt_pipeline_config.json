{"elt_pipelines": [{"pipeline_name": "user_events_silver", "pipeline_type": "silver", "source_table": "lakehouse.bronze_user_events", "target_table": "lakehouse.silver_user_events", "schedule": {"type": "interval", "interval_minutes": 15}, "processing_config": {"batch_size": 10000, "lookback_hours": 2, "watermark_column": "_processing_time", "deduplication": {"enabled": true, "key_columns": ["_kafka_topic", "_kafka_partition", "_kafka_offset"]}, "data_quality_rules": {"user_id": {"allow_null": false, "type": "str", "min_length": 1}, "timestamp": {"allow_null": false, "type": "str"}, "event_type": {"allow_null": false, "type": "str", "allowed_values": ["login", "logout", "page_view", "click", "purchase"]}}}, "transformations": [{"name": "parse_timestamp", "type": "datetime_parse", "source_column": "timestamp", "target_column": "event_timestamp", "format": "ISO8601"}, {"name": "extract_user_agent", "type": "regex_extract", "source_column": "user_agent", "pattern": "(?P<browser>\\w+)/(?P<version>[\\d.]+)", "target_columns": ["browser", "browser_version"]}, {"name": "categorize_event", "type": "categorize", "source_column": "event_type", "categories": {"login": "authentication", "logout": "authentication", "page_view": "navigation", "click": "interaction", "purchase": "transaction"}, "target_column": "event_category"}], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}}, {"pipeline_name": "transaction_logs_silver", "pipeline_type": "silver", "source_table": "lakehouse.bronze_transaction_logs", "target_table": "lakehouse.silver_transaction_logs", "schedule": {"type": "interval", "interval_minutes": 10}, "processing_config": {"batch_size": 20000, "lookback_hours": 1, "watermark_column": "_processing_time", "deduplication": {"enabled": true, "key_columns": ["transaction_id"]}, "data_quality_rules": {"transaction_id": {"allow_null": false, "type": "str", "min_length": 1}, "amount": {"allow_null": false, "type": "float", "min_value": 0.01, "max_value": 1000000.0}, "currency": {"allow_null": false, "type": "str", "allowed_values": ["USD", "EUR", "GBP", "JPY"]}}}, "transformations": [{"name": "normalize_currency", "type": "currency_conversion", "source_column": "amount", "currency_column": "currency", "target_currency": "USD", "target_column": "amount_usd"}, {"name": "categorize_transaction", "type": "categorize", "source_column": "transaction_type", "categories": {"purchase": "revenue", "refund": "revenue_adjustment", "fee": "cost", "transfer": "internal"}, "target_column": "transaction_category"}], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}}, {"pipeline_name": "user_events_gold_daily", "pipeline_type": "gold", "source_tables": ["lakehouse.silver_user_events"], "target_table": "lakehouse.gold_user_events_daily", "schedule": {"type": "cron", "cron_expression": "0 2 * * *", "timezone": "UTC"}, "processing_config": {"aggregation_window": "daily", "lookback_days": 7, "watermark_column": "event_timestamp"}, "aggregations": [{"name": "daily_user_activity", "group_by": ["user_id", "_event_date"], "metrics": [{"name": "total_events", "type": "count", "column": "*"}, {"name": "unique_sessions", "type": "count_distinct", "column": "session_id"}, {"name": "first_event_time", "type": "min", "column": "event_timestamp"}, {"name": "last_event_time", "type": "max", "column": "event_timestamp"}, {"name": "event_types", "type": "collect_list", "column": "event_type"}]}, {"name": "daily_event_summary", "group_by": ["event_category", "_event_date"], "metrics": [{"name": "event_count", "type": "count", "column": "*"}, {"name": "unique_users", "type": "count_distinct", "column": "user_id"}]}], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}}, {"pipeline_name": "transaction_logs_gold_hourly", "pipeline_type": "gold", "source_tables": ["lakehouse.silver_transaction_logs"], "target_table": "lakehouse.gold_transaction_logs_hourly", "schedule": {"type": "interval", "interval_minutes": 60}, "processing_config": {"aggregation_window": "hourly", "lookback_hours": 24, "watermark_column": "transaction_timestamp"}, "aggregations": [{"name": "hourly_transaction_summary", "group_by": ["transaction_category", "_event_year", "_event_month", "_event_day", "_event_hour"], "metrics": [{"name": "transaction_count", "type": "count", "column": "*"}, {"name": "total_amount_usd", "type": "sum", "column": "amount_usd"}, {"name": "avg_amount_usd", "type": "avg", "column": "amount_usd"}, {"name": "min_amount_usd", "type": "min", "column": "amount_usd"}, {"name": "max_amount_usd", "type": "max", "column": "amount_usd"}, {"name": "unique_users", "type": "count_distinct", "column": "user_id"}]}], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}}], "ray_config": {"address": "auto", "runtime_env": {"pip": ["getdaft", "<PERSON><PERSON><PERSON><PERSON>", "p<PERSON><PERSON>"]}}, "monitoring": {"enabled": true, "metrics_table": "lakehouse.elt_pipeline_metrics", "alert_thresholds": {"max_processing_delay_minutes": 30, "min_success_rate_percent": 95, "max_error_rate_percent": 5}}}