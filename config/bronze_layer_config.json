[{"consumer_name": "bronze_user_events", "topic_name": "user_events", "number_of_workers": 3, "enable_auto_commit": false, "bootstrap_servers": "localhost:19092,localhost:19094,localhost:19096", "key_deserializer": "STRING_DES", "value_deserializer": "STRING_DES", "header_deserializer": null, "auto_offset_reset": "earliest", "max_poll_records": 50, "max_poll_interval_ms": 300000, "sink_configs": {"transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer", "bronze_table_name": "bronze_user_events", "add_processing_metadata": true, "num_retries": 3, "retry_delay_seconds": 5, "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"], "batch_size": 1000, "batch_timeout_seconds": 30, "max_retries": 3, "table_name": "bronze_user_events", "database_name": "lakehouse", "partition_columns": ["_event_year", "_event_month"], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}, "daft_config": {"runner_config": {"use_ray": true, "ray_address": "auto"}}}, "dlq_config": {"bootstrap_servers": "localhost:19092,localhost:19094,localhost:19096", "topic_name": "bronze_user_events_dlq", "key_serializer": "STRING_SER", "value_serializer": "STRING_SER", "acks": "all", "compression_type": "gzip", "retries": 3, "linger_ms": 10}}, {"consumer_name": "bronze_transaction_logs", "topic_name": "transaction_logs", "number_of_workers": 2, "enable_auto_commit": false, "bootstrap_servers": "localhost:19092,localhost:19094,localhost:19096", "key_deserializer": "STRING_DES", "value_deserializer": "JSON_DES", "header_deserializer": null, "auto_offset_reset": "earliest", "max_poll_records": 100, "max_poll_interval_ms": 300000, "sink_configs": {"transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer", "bronze_table_name": "bronze_transaction_logs", "add_processing_metadata": true, "num_retries": 3, "retry_delay_seconds": 5, "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"], "batch_size": 2000, "batch_timeout_seconds": 60, "max_retries": 3, "table_name": "bronze_transaction_logs", "database_name": "lakehouse", "partition_columns": ["_event_year", "_event_month", "_event_day"], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}, "daft_config": {"runner_config": {"use_ray": true, "ray_address": "auto"}}}, "dlq_config": {"bootstrap_servers": "localhost:19092,localhost:19094,localhost:19096", "topic_name": "bronze_transaction_logs_dlq", "key_serializer": "STRING_SER", "value_serializer": "STRING_SER", "acks": "all", "compression_type": "gzip", "retries": 3, "linger_ms": 10}}, {"consumer_name": "bronze_system_metrics", "topic_name": "system_metrics", "number_of_workers": 1, "enable_auto_commit": false, "bootstrap_servers": "localhost:19092,localhost:19094,localhost:19096", "key_deserializer": "STRING_DES", "value_deserializer": "JSON_DES", "header_deserializer": null, "auto_offset_reset": "earliest", "max_poll_records": 200, "max_poll_interval_ms": 300000, "sink_configs": {"transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer", "bronze_table_name": "bronze_system_metrics", "add_processing_metadata": true, "num_retries": 3, "retry_delay_seconds": 5, "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"], "batch_size": 5000, "batch_timeout_seconds": 120, "max_retries": 3, "table_name": "bronze_system_metrics", "database_name": "lakehouse", "partition_columns": ["_event_year", "_event_month"], "iceberg_catalog": {"type": "hive", "uri": "thrift://localhost:9083", "warehouse": "s3://data-lake/warehouse", "s3.endpoint": "http://localhost:9000", "s3.access-key-id": "minioadmin", "s3.secret-access-key": "minioadmin", "s3.path-style-access": "true"}, "daft_config": {"runner_config": {"use_ray": true, "ray_address": "auto"}}}, "dlq_config": {"bootstrap_servers": "localhost:19092,localhost:19094,localhost:19096", "topic_name": "bronze_system_metrics_dlq", "key_serializer": "STRING_SER", "value_serializer": "STRING_SER", "acks": "all", "compression_type": "gzip", "retries": 3, "linger_ms": 10}}]