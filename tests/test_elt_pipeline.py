"""
Integration Tests for ELT Pipeline

Tests the complete Kafka → Daft → Iceberg pipeline including
Bronze, Silver, and Gold layer processing.
"""

import pytest
import json
import time
import tempfile
import os
from datetime import datetime, timezone
from typing import Dict, Any, List
from unittest.mock import Mock, patch, MagicMock

# Mock the dependencies that might not be available in test environment
try:
    import daft
    import ray
    from pyiceberg.catalog import load_catalog
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False
    # Create mock modules
    daft = Mock()
    ray = Mock()
    load_catalog = Mock()

from kafka.consumer.fetcher import ConsumerRecord

from src.transformers.daft_iceberg_transformer import (
    DaftIcebergTransformer, 
    SilverLayerTransformer, 
    GoldLayerTransformer
)
from src.model.worker_dto import SinkRecordDTO, SinkOperation, SinkOperationType


class TestDaftIcebergTransformer:
    """Test the Bronze layer transformer."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = {
            'bronze_table_name': 'test_bronze_events',
            'add_processing_metadata': True
        }
        self.transformer = DaftIcebergTransformer(self.config)
    
    def test_transform_json_message(self):
        """Test transforming a JSON message."""
        # Create mock consumer record
        message = {"user_id": "123", "event": "login", "timestamp": "2024-01-15T10:30:00Z"}
        consumer_record = Mock(spec=ConsumerRecord)
        consumer_record.value = json.dumps(message)
        consumer_record.key = "user_123"
        consumer_record.topic = "user_events"
        consumer_record.partition = 0
        consumer_record.offset = 12345
        consumer_record.timestamp = 1705315800000
        consumer_record.timestamp_type = 0
        consumer_record.headers = []
        
        # Transform the record
        result = self.transformer.transform(consumer_record)
        
        # Verify the result
        assert isinstance(result, SinkRecordDTO)
        assert result.key == "user_123"
        assert result.topic == "user_events"
        assert result.partition == 0
        assert result.offset == 12345
        
        # Check enriched message
        enriched = result.message
        assert enriched["user_id"] == "123"
        assert enriched["event"] == "login"
        assert enriched["_kafka_topic"] == "user_events"
        assert enriched["_kafka_partition"] == 0
        assert enriched["_kafka_offset"] == 12345
        assert enriched["_kafka_timestamp"] == 1705315800000
        assert "_processing_time" in enriched
        assert "_transformer_class" in enriched
    
    def test_transform_invalid_json(self):
        """Test handling of invalid JSON."""
        consumer_record = Mock(spec=ConsumerRecord)
        consumer_record.value = "invalid json {"
        consumer_record.key = "test_key"
        consumer_record.topic = "test_topic"
        consumer_record.partition = 0
        consumer_record.offset = 1
        consumer_record.timestamp = 1705315800000
        consumer_record.timestamp_type = 0
        consumer_record.headers = []
        
        # Should raise ValueError for invalid JSON
        with pytest.raises(ValueError, match="Failed to parse message as JSON"):
            self.transformer.transform(consumer_record)
    
    def test_transform_dict_message(self):
        """Test transforming a dict message (already parsed)."""
        message = {"user_id": "456", "action": "click"}
        consumer_record = Mock(spec=ConsumerRecord)
        consumer_record.value = message  # Already a dict
        consumer_record.key = "user_456"
        consumer_record.topic = "clicks"
        consumer_record.partition = 1
        consumer_record.offset = 67890
        consumer_record.timestamp = 1705315900000
        consumer_record.timestamp_type = 0
        consumer_record.headers = [("source", b"web")]
        
        result = self.transformer.transform(consumer_record)
        
        assert result.message["user_id"] == "456"
        assert result.message["action"] == "click"
        assert result.message["_kafka_headers"] == {"source": b"web"}


class TestSilverLayerTransformer:
    """Test the Silver layer transformer."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = {
            'silver_table_name': 'test_silver_events',
            'bronze_table_name': 'test_bronze_events',
            'data_quality_rules': {
                'user_id': {
                    'allow_null': False,
                    'type': str,
                    'min_length': 1
                },
                'amount': {
                    'allow_null': False,
                    'type': float,
                    'min_value': 0.01,
                    'max_value': 1000.0
                }
            }
        }
        self.transformer = SilverLayerTransformer(self.config)
    
    def test_apply_data_quality_rules(self):
        """Test data quality rule application."""
        message = {
            "user_id": "123",
            "amount": 50.0,
            "event_type": "purchase"
        }
        
        result = self.transformer._apply_data_quality_rules(message)
        
        # Should not have quality flags for valid data
        assert "user_id" in result
        assert "amount" in result
        assert result["user_id"] == "123"
        assert result["amount"] == 50.0
    
    def test_data_quality_rules_violations(self):
        """Test data quality rule violations."""
        message = {
            "user_id": None,  # Violates allow_null: False
            "amount": -10.0,  # Violates min_value: 0.01
            "event_type": "purchase"
        }
        
        result = self.transformer._apply_data_quality_rules(message)
        
        # Should have quality flags for invalid data
        assert "_user_id_quality_flag" in result
        assert "_amount_quality_flag" in result
    
    def test_enrich_for_silver_layer(self):
        """Test Silver layer enrichment."""
        message = {
            "user_id": "123",
            "_kafka_timestamp": 1705315800000  # 2024-01-15T10:30:00Z
        }
        
        result = self.transformer._enrich_for_silver_layer(message)
        
        assert "_silver_processing_time" in result
        assert "_silver_table" in result
        assert "_source_bronze_table" in result
        assert "_event_year" in result
        assert "_event_month" in result
        assert "_event_day" in result
        assert "_event_hour" in result
        assert "_event_date" in result
        
        # Check computed date fields
        assert result["_event_year"] == 2024
        assert result["_event_month"] == 1
        assert result["_event_day"] == 15
        assert result["_event_hour"] == 10


class TestGoldLayerTransformer:
    """Test the Gold layer transformer."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = {
            'gold_table_name': 'test_gold_events',
            'silver_table_name': 'test_silver_events',
            'aggregation_rules': {
                'user_category': {
                    'enabled': True,
                    'type': 'categorize',
                    'field': 'user_type',
                    'categories': {
                        'premium': 'high_value',
                        'basic': 'standard',
                        'trial': 'low_value'
                    }
                },
                'total_amount': {
                    'enabled': True,
                    'type': 'calculate',
                    'operation': 'sum',
                    'fields': ['amount', 'tax']
                }
            }
        }
        self.transformer = GoldLayerTransformer(self.config)
    
    def test_apply_business_logic(self):
        """Test business logic application."""
        message = {
            "user_id": "123",
            "user_type": "premium",
            "amount": 100.0,
            "tax": 10.0
        }
        
        result = self.transformer._apply_business_logic(message)
        
        assert "_gold_processing_time" in result
        assert "_gold_table" in result
        assert "_source_silver_table" in result
        assert "_business_user_category" in result
        assert "_business_total_amount" in result
        
        # Check business rule results
        assert result["_business_user_category"] == "high_value"
        assert result["_business_total_amount"] == 110.0
    
    def test_apply_business_rule_categorize(self):
        """Test categorization business rule."""
        message = {"user_type": "basic"}
        rule_config = {
            'type': 'categorize',
            'field': 'user_type',
            'categories': {
                'premium': 'high_value',
                'basic': 'standard',
                'trial': 'low_value'
            }
        }
        
        result = self.transformer._apply_business_rule(message, rule_config)
        assert result == "standard"
    
    def test_apply_business_rule_calculate(self):
        """Test calculation business rule."""
        message = {"amount": 100.0, "tax": 10.0, "fee": 5.0}
        rule_config = {
            'type': 'calculate',
            'operation': 'sum',
            'fields': ['amount', 'tax', 'fee']
        }
        
        result = self.transformer._apply_business_rule(message, rule_config)
        assert result == 115.0


@pytest.mark.skipif(not DEPENDENCIES_AVAILABLE, reason="Daft/Iceberg dependencies not available")
class TestBatchedDaftIcebergWriter:
    """Test the batched Daft-Iceberg writer."""
    
    def setup_method(self):
        """Setup test fixtures."""
        with patch('src.stream_writers.daft_iceberg_writer.load_catalog'):
            self.config = {
                'batch_size': 3,
                'batch_timeout_seconds': 1,
                'table_name': 'test_table',
                'database_name': 'test_db',
                'iceberg_catalog': {
                    'type': 'memory',  # Use in-memory catalog for testing
                }
            }
            
            # Mock the writer to avoid actual Iceberg operations
            with patch('src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter._initialize_iceberg'):
                from src.stream_writers.daft_iceberg_writer import BatchedDaftIcebergWriter
                self.writer = BatchedDaftIcebergWriter(self.config)
                self.writer.catalog = Mock()
                self.writer.table = Mock()
    
    def test_batching_by_size(self):
        """Test batching by size threshold."""
        # Create test records
        records = []
        for i in range(5):
            sink_record = Mock(spec=SinkRecordDTO)
            sink_record.message = {"id": i, "value": f"test_{i}"}
            sink_record.key = f"key_{i}"
            sink_record.topic = "test_topic"
            sink_record.partition = 0
            sink_record.offset = i
            records.append(sink_record)
        
        # Mock the flush method to track calls
        with patch.object(self.writer, '_flush_batch') as mock_flush:
            # Write records - should trigger flush when batch_size (3) is reached
            self.writer.write(records[:3])
            
            # Should have flushed once
            assert mock_flush.call_count == 1
            
            # Write more records
            self.writer.write(records[3:])
            
            # Should have flushed again
            assert mock_flush.call_count == 2
    
    def test_get_ordered_columns(self):
        """Test column ordering for consistent schema."""
        available_columns = [
            "user_id", "_kafka_offset", "event_type", "_kafka_topic",
            "_processing_time", "amount", "_event_year"
        ]
        
        ordered = self.writer._get_ordered_columns(available_columns)
        
        # Priority columns should come first
        priority_start = ["_kafka_topic", "_kafka_offset", "_processing_time", "_event_year"]
        for i, col in enumerate(priority_start):
            if col in ordered:
                assert ordered.index(col) < len(priority_start) + 3  # Allow some flexibility


@pytest.mark.skipif(not DEPENDENCIES_AVAILABLE, reason="Ray dependencies not available")
class TestELTOrchestrator:
    """Test the ELT orchestrator."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = {
            'elt_pipelines': [
                {
                    'pipeline_name': 'test_silver_pipeline',
                    'pipeline_type': 'silver',
                    'source_table': 'test.bronze_events',
                    'target_table': 'test.silver_events',
                    'schedule': {
                        'type': 'interval',
                        'interval_minutes': 5
                    },
                    'processing_config': {}
                }
            ],
            'ray_config': {
                'address': 'local'
            }
        }
    
    @patch('src.elt.orchestrator.ray')
    def test_create_pipeline_actors(self, mock_ray):
        """Test pipeline actor creation."""
        mock_ray.is_initialized.return_value = True
        
        from src.elt.orchestrator import ELTOrchestrator
        
        with patch.object(ELTOrchestrator, '_create_pipeline_actors'):
            orchestrator = ELTOrchestrator(self.config)
            
            # Should have created actors for each pipeline
            assert len(orchestrator.pipelines) == 1
    
    def test_should_execute_pipeline_interval(self):
        """Test interval-based pipeline scheduling."""
        from src.elt.orchestrator import ELTOrchestrator
        
        with patch.object(ELTOrchestrator, '_initialize_ray'), \
             patch.object(ELTOrchestrator, '_create_pipeline_actors'):
            orchestrator = ELTOrchestrator(self.config)
            
            schedule_config = {'type': 'interval', 'interval_minutes': 5}
            
            # Test with minute divisible by interval
            current_time = datetime(2024, 1, 15, 10, 5, 0, tzinfo=timezone.utc)
            assert orchestrator._should_execute_pipeline(schedule_config, current_time) == True
            
            # Test with minute not divisible by interval
            current_time = datetime(2024, 1, 15, 10, 3, 0, tzinfo=timezone.utc)
            assert orchestrator._should_execute_pipeline(schedule_config, current_time) == False


class TestIntegrationScenarios:
    """Integration test scenarios."""
    
    def test_end_to_end_data_flow(self):
        """Test complete data flow from Kafka to Gold layer."""
        # This would be a comprehensive integration test
        # For now, we'll test the component interactions
        
        # 1. Create sample Kafka message
        kafka_message = {
            "user_id": "test_user_123",
            "event_type": "purchase",
            "amount": 99.99,
            "timestamp": "2024-01-15T10:30:00Z"
        }
        
        # 2. Transform through Bronze layer
        bronze_config = {'bronze_table_name': 'test_bronze'}
        bronze_transformer = DaftIcebergTransformer(bronze_config)
        
        consumer_record = Mock(spec=ConsumerRecord)
        consumer_record.value = json.dumps(kafka_message)
        consumer_record.key = "test_user_123"
        consumer_record.topic = "purchases"
        consumer_record.partition = 0
        consumer_record.offset = 12345
        consumer_record.timestamp = 1705315800000
        consumer_record.timestamp_type = 0
        consumer_record.headers = []
        
        bronze_result = bronze_transformer.transform(consumer_record)
        
        # Verify Bronze transformation
        assert bronze_result.message["user_id"] == "test_user_123"
        assert bronze_result.message["_kafka_topic"] == "purchases"
        assert "_processing_time" in bronze_result.message
        
        # 3. Transform through Silver layer
        silver_config = {
            'silver_table_name': 'test_silver',
            'data_quality_rules': {
                'amount': {'min_value': 0.01, 'max_value': 1000.0}
            }
        }
        silver_transformer = SilverLayerTransformer(silver_config)
        
        # Simulate Silver processing (would normally read from Bronze Iceberg table)
        silver_message = bronze_result.message.copy()
        silver_result = silver_transformer._apply_data_quality_rules(silver_message)
        silver_result = silver_transformer._enrich_for_silver_layer(silver_result)
        
        # Verify Silver transformation
        assert "_silver_processing_time" in silver_result
        assert "_event_year" in silver_result
        
        # 4. Transform through Gold layer
        gold_config = {
            'gold_table_name': 'test_gold',
            'aggregation_rules': {
                'purchase_category': {
                    'enabled': True,
                    'type': 'categorize',
                    'field': 'amount',
                    'categories': {'99.99': 'high_value'}
                }
            }
        }
        gold_transformer = GoldLayerTransformer(gold_config)
        
        gold_result = gold_transformer._apply_business_logic(silver_result)
        
        # Verify Gold transformation
        assert "_gold_processing_time" in gold_result
        assert "_business_purchase_category" in gold_result
        
        print("✅ End-to-end data flow test completed successfully")
    
    def test_error_handling_scenarios(self):
        """Test error handling in various scenarios."""
        
        # Test invalid JSON handling
        bronze_transformer = DaftIcebergTransformer({})
        
        invalid_record = Mock(spec=ConsumerRecord)
        invalid_record.value = "invalid json {"
        invalid_record.key = "test"
        invalid_record.topic = "test"
        invalid_record.partition = 0
        invalid_record.offset = 1
        invalid_record.timestamp = 1705315800000
        invalid_record.timestamp_type = 0
        invalid_record.headers = []
        
        with pytest.raises(ValueError):
            bronze_transformer.transform(invalid_record)
        
        # Test missing required fields
        silver_transformer = SilverLayerTransformer({
            'data_quality_rules': {
                'required_field': {'allow_null': False}
            }
        })
        
        message_with_null = {'required_field': None, 'other_field': 'value'}
        result = silver_transformer._apply_data_quality_rules(message_with_null)
        
        # Should have quality flag for null value
        assert '_required_field_quality_flag' in result
        
        print("✅ Error handling scenarios test completed successfully")


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
