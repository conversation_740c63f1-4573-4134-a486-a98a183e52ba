fastapi==0.116.1
uvicorn==0.35.0
cachetools==6.1.0
starlette==0.47.3
pydantic==2.11.7
ratelimit==2.2.1
ray==2.48.0
setuptools==80.9.0
kafka-python==2.2.15

# Daft for distributed dataframe processing
getdaft==0.3.28

# Iceberg for data lake table format
pyiceberg==0.8.1

# Additional dependencies for Iceberg and data processing
pyarrow>=17.0.0
pandas>=2.0.0
fsspec>=2023.1.0
s3fs>=2023.1.0

# For Hive Metastore integration (optional)
thrift>=0.16.0
hive-metastore-client>=1.0.0

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
