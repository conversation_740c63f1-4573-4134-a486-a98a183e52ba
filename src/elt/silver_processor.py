"""
Silver Layer Processing Module

This module handles the Silver layer of the data lakehouse architecture,
which processes Bronze layer data to create cleaned, validated, and enriched datasets.

Silver layer characteristics:
- Data quality validation and cleansing
- Schema standardization and type conversion
- Business rule application
- Deduplication and data consistency
- Enrichment with additional context
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone, timedelta

try:
    import daft
    from pyiceberg.catalog import load_catalog
    from pyiceberg.table import Table
except ImportError as e:
    logging.error(f"Required dependencies not installed: {e}")
    raise

from src.utility.logging_util import get_logger

logger = get_logger(__name__)


class SilverLayerProcessor:
    """
    Processes Bronze layer data into Silver layer with data quality,
    validation, and business logic transformations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Silver layer processor.
        
        Args:
            config: Configuration dictionary containing processing rules,
                   data quality specifications, and Iceberg settings
        """
        self.config = config
        self.catalog_config = config.get('iceberg_catalog', {})
        self.database_name = config.get('database_name', 'lakehouse')
        
        # Processing configuration
        self.batch_size = config.get('batch_size', 10000)
        self.lookback_hours = config.get('lookback_hours', 2)
        self.watermark_column = config.get('watermark_column', '_processing_time')
        
        # Data quality configuration
        self.data_quality_rules = config.get('data_quality_rules', {})
        self.deduplication_config = config.get('deduplication', {})
        
        # Transformation configuration
        self.transformations = config.get('transformations', [])
        
        # Initialize Iceberg catalog
        self.catalog = None
        self.tables: Dict[str, Table] = {}
        self._initialize_catalog()
        
        logger.info(f"Initialized SilverLayerProcessor for database: {self.database_name}")
    
    def _initialize_catalog(self):
        """Initialize Iceberg catalog connection."""
        try:
            if not self.catalog_config:
                raise ValueError("Iceberg catalog configuration is required")
            
            self.catalog = load_catalog(name="silver_catalog", **self.catalog_config)
            logger.info("Successfully initialized Iceberg catalog for Silver layer")
            
        except Exception as e:
            logger.error(f"Failed to initialize Iceberg catalog: {e}")
            raise
    
    def process_bronze_to_silver(self, source_table: str, target_table: str,
                                processing_timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Process data from Bronze layer to Silver layer.
        
        Args:
            source_table: Bronze layer source table name
            target_table: Silver layer target table name
            processing_timestamp: Optional timestamp for incremental processing
            
        Returns:
            Dictionary containing processing statistics
        """
        if processing_timestamp is None:
            processing_timestamp = datetime.now(timezone.utc)
        
        logger.info(f"Starting Silver layer processing: {source_table} -> {target_table}")
        
        try:
            # Read data from Bronze layer
            bronze_df = self._read_bronze_data(source_table, processing_timestamp)
            
            if bronze_df.count_rows() == 0:
                logger.info("No new data to process in Bronze layer")
                return {'processed_rows': 0, 'status': 'no_data'}
            
            # Apply Silver layer transformations
            silver_df = self._apply_silver_transformations(bronze_df)
            
            # Write to Silver layer
            self._write_silver_data(silver_df, target_table)
            
            processed_rows = silver_df.count_rows()
            logger.info(f"Successfully processed {processed_rows} rows to Silver layer")
            
            return {
                'processed_rows': processed_rows,
                'status': 'success',
                'processing_timestamp': processing_timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to process Bronze to Silver: {e}")
            return {
                'processed_rows': 0,
                'status': 'error',
                'error': str(e),
                'processing_timestamp': processing_timestamp.isoformat()
            }
    
    def _read_bronze_data(self, table_name: str, processing_timestamp: datetime) -> daft.DataFrame:
        """
        Read incremental data from Bronze layer table.
        
        Args:
            table_name: Bronze layer table name
            processing_timestamp: Timestamp for incremental processing
            
        Returns:
            Daft DataFrame with Bronze layer data
        """
        try:
            # Load Bronze table
            bronze_table = self.catalog.load_table(table_name)
            
            # Calculate lookback timestamp
            lookback_timestamp = processing_timestamp - timedelta(hours=self.lookback_hours)
            
            # Read data with time-based filtering
            df = daft.read_iceberg(bronze_table)
            
            # Apply incremental filtering based on watermark column
            if self.watermark_column in df.column_names:
                df = df.filter(
                    daft.col(self.watermark_column) >= lookback_timestamp.isoformat()
                )
            
            logger.info(f"Read {df.count_rows()} rows from Bronze table: {table_name}")
            return df
            
        except Exception as e:
            logger.error(f"Failed to read Bronze data from {table_name}: {e}")
            raise
    
    def _apply_silver_transformations(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Apply Silver layer transformations including data quality, validation, and enrichment.
        
        Args:
            df: Input Bronze layer DataFrame
            
        Returns:
            Transformed Silver layer DataFrame
        """
        logger.info("Applying Silver layer transformations")
        
        # Apply deduplication if configured
        if self.deduplication_config.get('enabled', False):
            df = self._apply_deduplication(df)
        
        # Apply data quality rules
        df = self._apply_data_quality_rules(df)
        
        # Apply custom transformations
        for transformation in self.transformations:
            df = self._apply_transformation(df, transformation)
        
        # Add Silver layer metadata
        df = self._add_silver_metadata(df)
        
        return df
    
    def _apply_deduplication(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Apply deduplication logic to the DataFrame.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Deduplicated DataFrame
        """
        key_columns = self.deduplication_config.get('key_columns', [])
        
        if not key_columns:
            logger.warning("No deduplication key columns specified")
            return df
        
        # Check if all key columns exist
        available_columns = df.column_names
        valid_key_columns = [col for col in key_columns if col in available_columns]
        
        if not valid_key_columns:
            logger.warning(f"None of the deduplication key columns {key_columns} found in data")
            return df
        
        logger.info(f"Applying deduplication on columns: {valid_key_columns}")
        
        # Sort by processing time (descending) to keep latest records
        if '_processing_timestamp_ms' in available_columns:
            df = df.sort(daft.col('_processing_timestamp_ms').desc())
        
        # Drop duplicates based on key columns
        df = df.drop_duplicates(subset=valid_key_columns)
        
        return df
    
    def _apply_data_quality_rules(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Apply data quality rules and add quality flags.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with data quality flags
        """
        logger.info("Applying data quality rules")
        
        for field_name, rules in self.data_quality_rules.items():
            if field_name not in df.column_names:
                continue
            
            # Null value validation
            if not rules.get('allow_null', True):
                df = df.with_column(
                    f'{field_name}_quality_null_check',
                    daft.col(field_name).is_null()
                )
            
            # Type validation (simplified example)
            expected_type = rules.get('type')
            if expected_type == 'str':
                # Check if field can be converted to string
                df = df.with_column(
                    f'{field_name}_quality_type_check',
                    daft.col(field_name).cast(daft.DataType.string()).is_not_null()
                )
            
            # Range validation for numeric fields
            min_value = rules.get('min_value')
            max_value = rules.get('max_value')
            
            if min_value is not None:
                df = df.with_column(
                    f'{field_name}_quality_min_check',
                    daft.col(field_name) >= min_value
                )
            
            if max_value is not None:
                df = df.with_column(
                    f'{field_name}_quality_max_check',
                    daft.col(field_name) <= max_value
                )
            
            # Allowed values validation
            allowed_values = rules.get('allowed_values')
            if allowed_values:
                df = df.with_column(
                    f'{field_name}_quality_values_check',
                    daft.col(field_name).is_in(allowed_values)
                )
        
        return df
    
    def _apply_transformation(self, df: daft.DataFrame, transformation: Dict[str, Any]) -> daft.DataFrame:
        """
        Apply a single transformation to the DataFrame.
        
        Args:
            df: Input DataFrame
            transformation: Transformation configuration
            
        Returns:
            Transformed DataFrame
        """
        transform_type = transformation.get('type')
        transform_name = transformation.get('name', 'unnamed')
        
        logger.info(f"Applying transformation: {transform_name} (type: {transform_type})")
        
        try:
            if transform_type == 'datetime_parse':
                df = self._apply_datetime_parse(df, transformation)
            elif transform_type == 'regex_extract':
                df = self._apply_regex_extract(df, transformation)
            elif transform_type == 'categorize':
                df = self._apply_categorize(df, transformation)
            elif transform_type == 'currency_conversion':
                df = self._apply_currency_conversion(df, transformation)
            else:
                logger.warning(f"Unknown transformation type: {transform_type}")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to apply transformation {transform_name}: {e}")
            return df
    
    def _apply_datetime_parse(self, df: daft.DataFrame, config: Dict[str, Any]) -> daft.DataFrame:
        """Apply datetime parsing transformation."""
        source_column = config.get('source_column')
        target_column = config.get('target_column')
        
        if source_column not in df.column_names:
            return df
        
        # Parse datetime string to timestamp
        df = df.with_column(
            target_column,
            daft.col(source_column).str.strptime('%Y-%m-%dT%H:%M:%S%z')
        )
        
        return df
    
    def _apply_regex_extract(self, df: daft.DataFrame, config: Dict[str, Any]) -> daft.DataFrame:
        """Apply regex extraction transformation."""
        source_column = config.get('source_column')
        pattern = config.get('pattern')
        target_columns = config.get('target_columns', [])
        
        if source_column not in df.column_names or not pattern:
            return df
        
        # Extract using regex (simplified example)
        # In practice, you might need more sophisticated regex handling
        for i, target_col in enumerate(target_columns):
            df = df.with_column(
                target_col,
                daft.col(source_column).str.extract(pattern, i + 1)
            )
        
        return df
    
    def _apply_categorize(self, df: daft.DataFrame, config: Dict[str, Any]) -> daft.DataFrame:
        """Apply categorization transformation."""
        source_column = config.get('source_column')
        target_column = config.get('target_column')
        categories = config.get('categories', {})
        
        if source_column not in df.column_names:
            return df
        
        # Create categorization logic
        # This is a simplified example - you might want more sophisticated mapping
        category_expr = daft.lit('UNKNOWN')
        for value, category in categories.items():
            category_expr = daft.when(
                daft.col(source_column) == value, daft.lit(category)
            ).otherwise(category_expr)
        
        df = df.with_column(target_column, category_expr)
        
        return df
    
    def _apply_currency_conversion(self, df: daft.DataFrame, config: Dict[str, Any]) -> daft.DataFrame:
        """Apply currency conversion transformation."""
        source_column = config.get('source_column')
        target_column = config.get('target_column')
        target_currency = config.get('target_currency', 'USD')
        
        if source_column not in df.column_names:
            return df
        
        # Simplified currency conversion (in practice, you'd use real exchange rates)
        # For now, just copy the value assuming it's already in target currency
        df = df.with_column(target_column, daft.col(source_column))
        
        return df
    
    def _add_silver_metadata(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Add Silver layer metadata to the DataFrame.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with Silver layer metadata
        """
        current_time = datetime.now(timezone.utc).isoformat()
        
        df = df.with_column('_silver_processing_time', daft.lit(current_time))
        df = df.with_column('_silver_processor_version', daft.lit('1.0.0'))
        
        return df
    
    def _write_silver_data(self, df: daft.DataFrame, table_name: str):
        """
        Write DataFrame to Silver layer Iceberg table.
        
        Args:
            df: DataFrame to write
            table_name: Target table name
        """
        try:
            # Get or create Silver table
            silver_table = self._get_or_create_silver_table(table_name, df)
            
            # Write data
            df.write_iceberg(table=silver_table, mode="append")
            
            logger.info(f"Successfully wrote data to Silver table: {table_name}")
            
        except Exception as e:
            logger.error(f"Failed to write to Silver table {table_name}: {e}")
            raise
    
    def _get_or_create_silver_table(self, table_name: str, df: daft.DataFrame) -> Table:
        """
        Get existing Silver table or create new one.
        
        Args:
            table_name: Table name
            df: Sample DataFrame for schema inference
            
        Returns:
            Iceberg table
        """
        full_table_name = f"{self.database_name}.{table_name}"
        
        try:
            # Try to load existing table
            table = self.catalog.load_table(full_table_name)
            logger.info(f"Loaded existing Silver table: {full_table_name}")
            return table
        except Exception:
            # Create new table
            logger.info(f"Creating new Silver table: {full_table_name}")
            
            # Create schema from DataFrame
            # This is simplified - you might want more sophisticated schema handling
            from pyiceberg.schema import Schema
            from pyiceberg.types import StringType, NestedField
            
            fields = []
            for i, col_name in enumerate(df.column_names):
                fields.append(NestedField(i + 1, col_name, StringType(), required=False))
            
            schema = Schema(*fields)
            
            table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                properties={
                    'write.format.default': 'parquet',
                    'write.parquet.compression-codec': 'zstd'
                }
            )
            
            return table
