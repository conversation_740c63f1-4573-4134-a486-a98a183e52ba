"""
ELT Orchestration Module

This module provides Ray-based orchestration for ELT pipelines,
managing the execution of Bronze -> Silver -> Gold transformations
with scheduling, monitoring, and error handling.
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from threading import Event, Thread

try:
    import ray
    from ray import serve
except ImportError as e:
    logging.error(f"Ray not installed: {e}")
    raise

from src.elt.silver_processor import SilverLayerProcessor
from src.elt.gold_processor import GoldLayerProcessor
from src.utility.logging_util import get_logger

logger = get_logger(__name__)


@ray.remote
class ELTPipelineActor:
    """
    Ray actor for executing individual ELT pipeline steps.
    """
    
    def __init__(self, pipeline_config: Dict[str, Any]):
        """
        Initialize ELT pipeline actor.
        
        Args:
            pipeline_config: Pipeline configuration dictionary
        """
        self.config = pipeline_config
        self.pipeline_name = pipeline_config.get('pipeline_name', 'unknown')
        self.pipeline_type = pipeline_config.get('pipeline_type', 'silver')
        
        # Initialize processors based on pipeline type
        if self.pipeline_type == 'silver':
            self.processor = SilverLayerProcessor(pipeline_config.get('processing_config', {}))
        elif self.pipeline_type == 'gold':
            self.processor = GoldLayerProcessor(pipeline_config.get('processing_config', {}))
        else:
            raise ValueError(f"Unknown pipeline type: {self.pipeline_type}")
        
        logger.info(f"Initialized ELT pipeline actor: {self.pipeline_name} ({self.pipeline_type})")
    
    def execute_pipeline(self, processing_timestamp: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute the ELT pipeline.
        
        Args:
            processing_timestamp: Optional ISO timestamp for processing
            
        Returns:
            Dictionary containing execution results
        """
        start_time = time.time()
        timestamp = datetime.fromisoformat(processing_timestamp) if processing_timestamp else None
        
        logger.info(f"Executing pipeline: {self.pipeline_name}")
        
        try:
            if self.pipeline_type == 'silver':
                result = self._execute_silver_pipeline(timestamp)
            elif self.pipeline_type == 'gold':
                result = self._execute_gold_pipeline(timestamp)
            else:
                raise ValueError(f"Unknown pipeline type: {self.pipeline_type}")
            
            execution_time = time.time() - start_time
            result['execution_time_seconds'] = execution_time
            result['pipeline_name'] = self.pipeline_name
            result['pipeline_type'] = self.pipeline_type
            
            logger.info(f"Pipeline {self.pipeline_name} completed in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_result = {
                'status': 'error',
                'error': str(e),
                'execution_time_seconds': execution_time,
                'pipeline_name': self.pipeline_name,
                'pipeline_type': self.pipeline_type
            }
            logger.error(f"Pipeline {self.pipeline_name} failed: {e}")
            return error_result
    
    def _execute_silver_pipeline(self, timestamp: Optional[datetime]) -> Dict[str, Any]:
        """Execute Silver layer pipeline."""
        source_table = self.config.get('source_table')
        target_table = self.config.get('target_table')
        
        if not source_table or not target_table:
            raise ValueError("Silver pipeline requires source_table and target_table")
        
        return self.processor.process_bronze_to_silver(source_table, target_table, timestamp)
    
    def _execute_gold_pipeline(self, timestamp: Optional[datetime]) -> Dict[str, Any]:
        """Execute Gold layer pipeline."""
        source_tables = self.config.get('source_tables', [])
        target_table = self.config.get('target_table')
        
        if not source_tables or not target_table:
            raise ValueError("Gold pipeline requires source_tables and target_table")
        
        return self.processor.process_silver_to_gold(source_tables, target_table, timestamp)
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status."""
        return {
            'pipeline_name': self.pipeline_name,
            'pipeline_type': self.pipeline_type,
            'status': 'ready',
            'last_execution': None  # Would track in production
        }


class ELTOrchestrator:
    """
    Orchestrates ELT pipelines using Ray for distributed execution.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize ELT orchestrator.
        
        Args:
            config: Orchestrator configuration
        """
        self.config = config
        self.pipelines = config.get('elt_pipelines', [])
        self.ray_config = config.get('ray_config', {})
        self.monitoring_config = config.get('monitoring', {})
        
        # Pipeline actors
        self.pipeline_actors: Dict[str, ray.ObjectRef] = {}
        
        # Scheduling
        self.scheduler_thread: Optional[Thread] = None
        self.stop_event = Event()
        
        # Initialize Ray
        self._initialize_ray()
        
        # Create pipeline actors
        self._create_pipeline_actors()
        
        logger.info(f"Initialized ELT orchestrator with {len(self.pipelines)} pipelines")
    
    def _initialize_ray(self):
        """Initialize Ray cluster connection."""
        try:
            ray_address = self.ray_config.get('address', 'auto')
            runtime_env = self.ray_config.get('runtime_env', {})
            
            if not ray.is_initialized():
                ray.init(address=ray_address, runtime_env=runtime_env)
                logger.info(f"Initialized Ray connection to: {ray_address}")
            else:
                logger.info("Ray already initialized")
                
        except Exception as e:
            logger.error(f"Failed to initialize Ray: {e}")
            raise
    
    def _create_pipeline_actors(self):
        """Create Ray actors for each pipeline."""
        for pipeline_config in self.pipelines:
            pipeline_name = pipeline_config.get('pipeline_name')
            
            try:
                actor = ELTPipelineActor.remote(pipeline_config)
                self.pipeline_actors[pipeline_name] = actor
                logger.info(f"Created pipeline actor: {pipeline_name}")
                
            except Exception as e:
                logger.error(f"Failed to create pipeline actor {pipeline_name}: {e}")
    
    def start_scheduler(self):
        """Start the pipeline scheduler."""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            logger.warning("Scheduler already running")
            return
        
        self.stop_event.clear()
        self.scheduler_thread = Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Started ELT pipeline scheduler")
    
    def stop_scheduler(self):
        """Stop the pipeline scheduler."""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.stop_event.set()
            self.scheduler_thread.join(timeout=10)
            logger.info("Stopped ELT pipeline scheduler")
    
    def _scheduler_loop(self):
        """Main scheduler loop."""
        logger.info("ELT scheduler loop started")
        
        while not self.stop_event.is_set():
            try:
                current_time = datetime.now(timezone.utc)
                
                # Check each pipeline for scheduled execution
                for pipeline_config in self.pipelines:
                    pipeline_name = pipeline_config.get('pipeline_name')
                    schedule_config = pipeline_config.get('schedule', {})
                    
                    if self._should_execute_pipeline(schedule_config, current_time):
                        logger.info(f"Triggering scheduled execution of pipeline: {pipeline_name}")
                        self.execute_pipeline_async(pipeline_name)
                
                # Sleep for 60 seconds before next check
                self.stop_event.wait(60)
                
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                self.stop_event.wait(60)
        
        logger.info("ELT scheduler loop stopped")
    
    def _should_execute_pipeline(self, schedule_config: Dict[str, Any], current_time: datetime) -> bool:
        """
        Check if pipeline should be executed based on schedule.
        
        Args:
            schedule_config: Schedule configuration
            current_time: Current timestamp
            
        Returns:
            True if pipeline should be executed
        """
        schedule_type = schedule_config.get('type', 'manual')
        
        if schedule_type == 'interval':
            interval_minutes = schedule_config.get('interval_minutes', 60)
            # Simplified interval check - in production, you'd track last execution
            return current_time.minute % interval_minutes == 0
        
        elif schedule_type == 'cron':
            # Simplified cron check - in production, use proper cron library
            cron_expression = schedule_config.get('cron_expression', '0 2 * * *')
            # For now, just check if it's 2 AM for daily jobs
            return current_time.hour == 2 and current_time.minute == 0
        
        return False
    
    def execute_pipeline_async(self, pipeline_name: str, 
                              processing_timestamp: Optional[str] = None) -> ray.ObjectRef:
        """
        Execute pipeline asynchronously.
        
        Args:
            pipeline_name: Name of the pipeline to execute
            processing_timestamp: Optional processing timestamp
            
        Returns:
            Ray ObjectRef for the execution result
        """
        if pipeline_name not in self.pipeline_actors:
            raise ValueError(f"Pipeline not found: {pipeline_name}")
        
        actor = self.pipeline_actors[pipeline_name]
        result_ref = actor.execute_pipeline.remote(processing_timestamp)
        
        logger.info(f"Started async execution of pipeline: {pipeline_name}")
        return result_ref
    
    def execute_pipeline_sync(self, pipeline_name: str, 
                             processing_timestamp: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute pipeline synchronously.
        
        Args:
            pipeline_name: Name of the pipeline to execute
            processing_timestamp: Optional processing timestamp
            
        Returns:
            Execution result dictionary
        """
        result_ref = self.execute_pipeline_async(pipeline_name, processing_timestamp)
        return ray.get(result_ref)
    
    def execute_all_pipelines_async(self, processing_timestamp: Optional[str] = None) -> List[ray.ObjectRef]:
        """
        Execute all pipelines asynchronously.
        
        Args:
            processing_timestamp: Optional processing timestamp
            
        Returns:
            List of Ray ObjectRefs for execution results
        """
        result_refs = []
        
        for pipeline_name in self.pipeline_actors.keys():
            try:
                result_ref = self.execute_pipeline_async(pipeline_name, processing_timestamp)
                result_refs.append(result_ref)
            except Exception as e:
                logger.error(f"Failed to start pipeline {pipeline_name}: {e}")
        
        logger.info(f"Started async execution of {len(result_refs)} pipelines")
        return result_refs
    
    def wait_for_pipelines(self, result_refs: List[ray.ObjectRef], 
                          timeout: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Wait for pipeline executions to complete.
        
        Args:
            result_refs: List of Ray ObjectRefs
            timeout: Optional timeout in seconds
            
        Returns:
            List of execution results
        """
        try:
            if timeout:
                ready_refs, remaining_refs = ray.wait(result_refs, timeout=timeout)
                results = ray.get(ready_refs)
                
                # Log incomplete pipelines
                if remaining_refs:
                    logger.warning(f"{len(remaining_refs)} pipelines did not complete within timeout")
                
                return results
            else:
                return ray.get(result_refs)
                
        except Exception as e:
            logger.error(f"Error waiting for pipelines: {e}")
            return []
    
    def get_pipeline_status(self, pipeline_name: str) -> Dict[str, Any]:
        """
        Get status of a specific pipeline.
        
        Args:
            pipeline_name: Name of the pipeline
            
        Returns:
            Pipeline status dictionary
        """
        if pipeline_name not in self.pipeline_actors:
            return {'error': f'Pipeline not found: {pipeline_name}'}
        
        try:
            actor = self.pipeline_actors[pipeline_name]
            status_ref = actor.get_pipeline_status.remote()
            return ray.get(status_ref)
        except Exception as e:
            return {'error': f'Failed to get status: {e}'}
    
    def get_all_pipeline_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all pipelines.
        
        Returns:
            Dictionary mapping pipeline names to their status
        """
        status_dict = {}
        
        for pipeline_name in self.pipeline_actors.keys():
            status_dict[pipeline_name] = self.get_pipeline_status(pipeline_name)
        
        return status_dict
    
    def shutdown(self):
        """Shutdown the orchestrator and cleanup resources."""
        logger.info("Shutting down ELT orchestrator")
        
        # Stop scheduler
        self.stop_scheduler()
        
        # Kill pipeline actors
        for pipeline_name, actor in self.pipeline_actors.items():
            try:
                ray.kill(actor)
                logger.info(f"Killed pipeline actor: {pipeline_name}")
            except Exception as e:
                logger.error(f"Failed to kill pipeline actor {pipeline_name}: {e}")
        
        self.pipeline_actors.clear()
        
        logger.info("ELT orchestrator shutdown complete")


def create_orchestrator_from_config(config_path: str) -> ELTOrchestrator:
    """
    Create ELT orchestrator from configuration file.
    
    Args:
        config_path: Path to configuration JSON file
        
    Returns:
        Initialized ELTOrchestrator instance
    """
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        return ELTOrchestrator(config)
        
    except Exception as e:
        logger.error(f"Failed to create orchestrator from config {config_path}: {e}")
        raise
