"""
ELT (Extract, Load, Transform) Package

This package contains modules for implementing the Bronze-Silver-Gold
data lakehouse architecture using Daft and Iceberg.

Modules:
- bronze_processor: Handles Bronze layer (raw data ingestion)
- silver_processor: Handles Silver layer (cleaned and enriched data)
- gold_processor: Handles Gold layer (business-ready aggregated data)
- orchestrator: Orchestrates ELT pipelines using Ray
"""

from .bronze_processor import BronzeLayerProcessor
from .silver_processor import SilverLayerProcessor
from .gold_processor import GoldLayerProcessor
from .orchestrator import ELTOrchestrator, create_orchestrator_from_config

__all__ = [
    'BronzeLayerProcessor',
    'SilverLayerProcessor',
    'GoldLayerProcessor',
    'ELTOrchestrator',
    'create_orchestrator_from_config'
]
