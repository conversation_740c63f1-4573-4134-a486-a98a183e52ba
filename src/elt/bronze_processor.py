"""
Bronze Layer Processing Module

This module handles the Bronze layer of the data lakehouse architecture,
which stores raw, enriched data from Kafka sources in Iceberg tables.

Bronze layer characteristics:
- Raw data with minimal transformation
- Enriched with Kafka metadata for lineage
- Partitioned by time for efficient querying
- Serves as the source of truth for all downstream processing
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

try:
    import daft
    from pyiceberg.catalog import load_catalog
    from pyiceberg.table import Table
    from pyiceberg.schema import Schema
    from pyiceberg.types import (
        StringType, IntegerType, LongType, DoubleType, 
        BooleanType, TimestampType, NestedField
    )
except ImportError as e:
    logging.error(f"Required dependencies not installed: {e}")
    raise

from src.utility.logging_util import get_logger

logger = get_logger(__name__)


class BronzeLayerProcessor:
    """
    Processes raw Kafka data into Bronze layer Iceberg tables.
    
    The Bronze layer serves as the landing zone for all raw data with minimal
    transformation, focusing on data ingestion, enrichment with metadata,
    and efficient storage for downstream processing.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Bronze layer processor.
        
        Args:
            config: Configuration dictionary containing Iceberg catalog settings,
                   table configurations, and processing parameters
        """
        self.config = config
        self.catalog_config = config.get('iceberg_catalog', {})
        self.database_name = config.get('database_name', 'lakehouse')
        self.partition_columns = config.get('partition_columns', ['_event_year', '_event_month'])
        
        # Initialize Iceberg catalog
        self.catalog = None
        self.tables: Dict[str, Table] = {}
        self._initialize_catalog()
        
        logger.info(f"Initialized BronzeLayerProcessor for database: {self.database_name}")
    
    def _initialize_catalog(self):
        """Initialize Iceberg catalog connection."""
        try:
            if not self.catalog_config:
                raise ValueError("Iceberg catalog configuration is required")
            
            self.catalog = load_catalog(name="bronze_catalog", **self.catalog_config)
            logger.info("Successfully initialized Iceberg catalog for Bronze layer")
            
        except Exception as e:
            logger.error(f"Failed to initialize Iceberg catalog: {e}")
            raise
    
    def create_bronze_table(self, table_name: str, sample_data: Optional[List[Dict[str, Any]]] = None) -> Table:
        """
        Create a Bronze layer Iceberg table with appropriate schema and partitioning.
        
        Args:
            table_name: Name of the table to create
            sample_data: Optional sample data to infer additional schema fields
            
        Returns:
            Created Iceberg table
        """
        full_table_name = f"{self.database_name}.{table_name}"
        
        try:
            # Check if table already exists
            try:
                existing_table = self.catalog.load_table(full_table_name)
                logger.info(f"Bronze table {full_table_name} already exists")
                return existing_table
            except Exception:
                # Table doesn't exist, create it
                pass
            
            # Create schema for Bronze layer
            schema = self._create_bronze_schema(sample_data)
            
            # Create partition specification
            partition_spec = []
            for col in self.partition_columns:
                if col in [field.name for field in schema.fields]:
                    partition_spec.append(col)
            
            # Create the table
            table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                partition_spec=partition_spec if partition_spec else None,
                properties={
                    'write.format.default': 'parquet',
                    'write.parquet.compression-codec': 'zstd',
                    'write.target-file-size-bytes': '134217728',  # 128MB
                    'write.metadata.delete-after-commit.enabled': 'true',
                    'write.metadata.previous-versions-max': '10'
                }
            )
            
            self.tables[table_name] = table
            logger.info(f"Created Bronze table: {full_table_name} with partitioning: {partition_spec}")
            
            return table
            
        except Exception as e:
            logger.error(f"Failed to create Bronze table {full_table_name}: {e}")
            raise
    
    def _create_bronze_schema(self, sample_data: Optional[List[Dict[str, Any]]] = None) -> Schema:
        """
        Create Iceberg schema for Bronze layer tables.
        
        Args:
            sample_data: Optional sample data to infer additional fields
            
        Returns:
            Iceberg schema for Bronze layer
        """
        fields = []
        field_id = 1
        
        # Standard Kafka metadata fields
        kafka_fields = [
            ("_kafka_key", StringType(), False),
            ("_kafka_topic", StringType(), True),
            ("_kafka_partition", IntegerType(), True),
            ("_kafka_offset", LongType(), True),
            ("_kafka_timestamp", LongType(), False),
            ("_kafka_timestamp_type", IntegerType(), False),
            ("_kafka_headers", StringType(), False),  # JSON string of headers
        ]
        
        for field_name, field_type, required in kafka_fields:
            fields.append(NestedField(field_id, field_name, field_type, required=required))
            field_id += 1
        
        # Processing metadata fields
        processing_fields = [
            ("_processing_time", StringType(), False),
            ("_processing_timestamp_ms", LongType(), False),
            ("_transformer_class", StringType(), False),
            ("_bronze_table", StringType(), False),
            ("_write_timestamp", StringType(), False),
        ]
        
        for field_name, field_type, required in processing_fields:
            fields.append(NestedField(field_id, field_name, field_type, required=required))
            field_id += 1
        
        # Partitioning fields
        partition_fields = [
            ("_event_year", IntegerType(), False),
            ("_event_month", IntegerType(), False),
            ("_event_day", IntegerType(), False),
            ("_event_hour", IntegerType(), False),
        ]
        
        for field_name, field_type, required in partition_fields:
            fields.append(NestedField(field_id, field_name, field_type, required=required))
            field_id += 1
        
        # Sink metadata fields
        sink_fields = [
            ("_sink_key", StringType(), False),
            ("_sink_topic", StringType(), False),
            ("_sink_partition", IntegerType(), False),
            ("_sink_offset", LongType(), False),
        ]
        
        for field_name, field_type, required in sink_fields:
            fields.append(NestedField(field_id, field_name, field_type, required=required))
            field_id += 1
        
        # If sample data is provided, infer additional fields
        if sample_data:
            additional_fields = self._infer_fields_from_sample_data(sample_data, field_id)
            fields.extend(additional_fields)
        
        return Schema(*fields)
    
    def _infer_fields_from_sample_data(self, sample_data: List[Dict[str, Any]], 
                                     start_field_id: int) -> List[NestedField]:
        """
        Infer additional schema fields from sample data.
        
        Args:
            sample_data: Sample data records
            start_field_id: Starting field ID for new fields
            
        Returns:
            List of additional NestedField objects
        """
        fields = []
        field_id = start_field_id
        
        # Collect all unique field names from sample data
        all_fields = set()
        for record in sample_data:
            all_fields.update(record.keys())
        
        # Remove fields that are already defined
        existing_fields = {
            '_kafka_key', '_kafka_topic', '_kafka_partition', '_kafka_offset',
            '_kafka_timestamp', '_kafka_timestamp_type', '_kafka_headers',
            '_processing_time', '_processing_timestamp_ms', '_transformer_class',
            '_bronze_table', '_write_timestamp', '_event_year', '_event_month',
            '_event_day', '_event_hour', '_sink_key', '_sink_topic',
            '_sink_partition', '_sink_offset'
        }
        
        new_fields = all_fields - existing_fields
        
        # Infer types for new fields
        for field_name in sorted(new_fields):
            field_type = self._infer_field_type(field_name, sample_data)
            fields.append(NestedField(field_id, field_name, field_type, required=False))
            field_id += 1
        
        return fields
    
    def _infer_field_type(self, field_name: str, sample_data: List[Dict[str, Any]]) -> Any:
        """
        Infer Iceberg field type from sample data.
        
        Args:
            field_name: Name of the field
            sample_data: Sample data records
            
        Returns:
            Iceberg field type
        """
        # Collect non-null values for this field
        values = []
        for record in sample_data:
            if field_name in record and record[field_name] is not None:
                values.append(record[field_name])
        
        if not values:
            return StringType()  # Default to string for null fields
        
        # Check types of values
        types = set(type(v).__name__ for v in values)
        
        if len(types) == 1:
            type_name = types.pop()
            if type_name == 'str':
                return StringType()
            elif type_name == 'int':
                return LongType()  # Use LongType for integers to avoid overflow
            elif type_name == 'float':
                return DoubleType()
            elif type_name == 'bool':
                return BooleanType()
        
        # Mixed types or unknown, default to string
        return StringType()
    
    def get_or_create_table(self, table_name: str, 
                           sample_data: Optional[List[Dict[str, Any]]] = None) -> Table:
        """
        Get existing table or create new Bronze layer table.
        
        Args:
            table_name: Name of the table
            sample_data: Optional sample data for schema inference
            
        Returns:
            Iceberg table
        """
        if table_name in self.tables:
            return self.tables[table_name]
        
        full_table_name = f"{self.database_name}.{table_name}"
        
        try:
            # Try to load existing table
            table = self.catalog.load_table(full_table_name)
            self.tables[table_name] = table
            logger.info(f"Loaded existing Bronze table: {full_table_name}")
            return table
        except Exception:
            # Table doesn't exist, create it
            return self.create_bronze_table(table_name, sample_data)
    
    def write_to_bronze_table(self, table_name: str, df: daft.DataFrame) -> None:
        """
        Write Daft DataFrame to Bronze layer Iceberg table.
        
        Args:
            table_name: Name of the target table
            df: Daft DataFrame to write
        """
        try:
            table = self.get_or_create_table(table_name)
            
            # Write DataFrame to Iceberg table
            df.write_iceberg(table=table, mode="append")
            
            logger.info(f"Successfully wrote data to Bronze table: {table_name}")
            
        except Exception as e:
            logger.error(f"Failed to write to Bronze table {table_name}: {e}")
            raise
    
    def optimize_table(self, table_name: str) -> None:
        """
        Optimize Bronze layer table by compacting small files.
        
        Args:
            table_name: Name of the table to optimize
        """
        try:
            table = self.get_or_create_table(table_name)
            
            # Trigger table optimization (compaction)
            # Note: This is a simplified example. In practice, you might want
            # to use more sophisticated optimization strategies
            logger.info(f"Optimizing Bronze table: {table_name}")
            
            # You can implement custom optimization logic here
            # For example, using Iceberg's maintenance operations
            
        except Exception as e:
            logger.error(f"Failed to optimize Bronze table {table_name}: {e}")
            raise
    
    def get_table_stats(self, table_name: str) -> Dict[str, Any]:
        """
        Get statistics for a Bronze layer table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            Dictionary containing table statistics
        """
        try:
            table = self.get_or_create_table(table_name)
            
            # Get table metadata
            metadata = table.metadata
            
            stats = {
                'table_name': table_name,
                'schema_id': metadata.current_schema_id,
                'partition_spec_id': metadata.default_spec_id,
                'last_updated': metadata.last_updated_ms,
                'location': metadata.location,
                'properties': metadata.properties,
                'snapshots_count': len(metadata.snapshots) if metadata.snapshots else 0
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get stats for Bronze table {table_name}: {e}")
            raise
