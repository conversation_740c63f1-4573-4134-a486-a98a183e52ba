"""
Gold Layer Processing Module

This module handles the Gold layer of the data lakehouse architecture,
which creates business-ready, aggregated datasets from Silver layer data.

Gold layer characteristics:
- Business-focused aggregations and metrics
- Optimized for analytics and reporting
- Pre-computed KPIs and business intelligence data
- Highly performant for end-user consumption
- Denormalized for fast query performance
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone, timedelta

try:
    import daft
    from pyiceberg.catalog import load_catalog
    from pyiceberg.table import Table
except ImportError as e:
    logging.error(f"Required dependencies not installed: {e}")
    raise

from src.utility.logging_util import get_logger

logger = get_logger(__name__)


class GoldLayerProcessor:
    """
    Processes Silver layer data into Gold layer with business aggregations,
    KPIs, and analytics-ready datasets.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Gold layer processor.
        
        Args:
            config: Configuration dictionary containing aggregation rules,
                   business logic, and Iceberg settings
        """
        self.config = config
        self.catalog_config = config.get('iceberg_catalog', {})
        self.database_name = config.get('database_name', 'lakehouse')
        
        # Processing configuration
        self.aggregation_window = config.get('aggregation_window', 'daily')
        self.lookback_days = config.get('lookback_days', 7)
        self.watermark_column = config.get('watermark_column', 'event_timestamp')
        
        # Aggregation configuration
        self.aggregations = config.get('aggregations', [])
        
        # Initialize Iceberg catalog
        self.catalog = None
        self.tables: Dict[str, Table] = {}
        self._initialize_catalog()
        
        logger.info(f"Initialized GoldLayerProcessor for database: {self.database_name}")
    
    def _initialize_catalog(self):
        """Initialize Iceberg catalog connection."""
        try:
            if not self.catalog_config:
                raise ValueError("Iceberg catalog configuration is required")
            
            self.catalog = load_catalog(name="gold_catalog", **self.catalog_config)
            logger.info("Successfully initialized Iceberg catalog for Gold layer")
            
        except Exception as e:
            logger.error(f"Failed to initialize Iceberg catalog: {e}")
            raise
    
    def process_silver_to_gold(self, source_tables: List[str], target_table: str,
                              processing_timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Process data from Silver layer to Gold layer with business aggregations.
        
        Args:
            source_tables: List of Silver layer source table names
            target_table: Gold layer target table name
            processing_timestamp: Optional timestamp for incremental processing
            
        Returns:
            Dictionary containing processing statistics
        """
        if processing_timestamp is None:
            processing_timestamp = datetime.now(timezone.utc)
        
        logger.info(f"Starting Gold layer processing: {source_tables} -> {target_table}")
        
        try:
            # Read data from Silver layer tables
            silver_dfs = []
            for source_table in source_tables:
                df = self._read_silver_data(source_table, processing_timestamp)
                if df.count_rows() > 0:
                    silver_dfs.append(df)
            
            if not silver_dfs:
                logger.info("No new data to process in Silver layer")
                return {'processed_rows': 0, 'status': 'no_data'}
            
            # Combine Silver layer data if multiple sources
            combined_df = silver_dfs[0]
            for df in silver_dfs[1:]:
                combined_df = combined_df.concat(df)
            
            # Apply Gold layer aggregations
            gold_dfs = self._apply_gold_aggregations(combined_df)
            
            # Write aggregated data to Gold layer
            total_rows = 0
            for agg_name, gold_df in gold_dfs.items():
                gold_table_name = f"{target_table}_{agg_name}"
                self._write_gold_data(gold_df, gold_table_name)
                total_rows += gold_df.count_rows()
            
            logger.info(f"Successfully processed {total_rows} aggregated rows to Gold layer")
            
            return {
                'processed_rows': total_rows,
                'aggregations_created': len(gold_dfs),
                'status': 'success',
                'processing_timestamp': processing_timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to process Silver to Gold: {e}")
            return {
                'processed_rows': 0,
                'status': 'error',
                'error': str(e),
                'processing_timestamp': processing_timestamp.isoformat()
            }
    
    def _read_silver_data(self, table_name: str, processing_timestamp: datetime) -> daft.DataFrame:
        """
        Read incremental data from Silver layer table.
        
        Args:
            table_name: Silver layer table name
            processing_timestamp: Timestamp for incremental processing
            
        Returns:
            Daft DataFrame with Silver layer data
        """
        try:
            # Load Silver table
            silver_table = self.catalog.load_table(table_name)
            
            # Calculate lookback timestamp
            lookback_timestamp = processing_timestamp - timedelta(days=self.lookback_days)
            
            # Read data with time-based filtering
            df = daft.read_iceberg(silver_table)
            
            # Apply incremental filtering based on watermark column
            if self.watermark_column in df.column_names:
                df = df.filter(
                    daft.col(self.watermark_column) >= lookback_timestamp.isoformat()
                )
            
            logger.info(f"Read {df.count_rows()} rows from Silver table: {table_name}")
            return df
            
        except Exception as e:
            logger.error(f"Failed to read Silver data from {table_name}: {e}")
            raise
    
    def _apply_gold_aggregations(self, df: daft.DataFrame) -> Dict[str, daft.DataFrame]:
        """
        Apply Gold layer aggregations to create business-ready datasets.
        
        Args:
            df: Input Silver layer DataFrame
            
        Returns:
            Dictionary of aggregated DataFrames by aggregation name
        """
        logger.info("Applying Gold layer aggregations")
        
        aggregated_dfs = {}
        
        for aggregation in self.aggregations:
            agg_name = aggregation.get('name', 'unnamed')
            
            try:
                logger.info(f"Processing aggregation: {agg_name}")
                agg_df = self._apply_single_aggregation(df, aggregation)
                
                if agg_df.count_rows() > 0:
                    aggregated_dfs[agg_name] = agg_df
                    logger.info(f"Created {agg_df.count_rows()} aggregated rows for {agg_name}")
                
            except Exception as e:
                logger.error(f"Failed to process aggregation {agg_name}: {e}")
                continue
        
        return aggregated_dfs
    
    def _apply_single_aggregation(self, df: daft.DataFrame, aggregation: Dict[str, Any]) -> daft.DataFrame:
        """
        Apply a single aggregation configuration to the DataFrame.
        
        Args:
            df: Input DataFrame
            aggregation: Aggregation configuration
            
        Returns:
            Aggregated DataFrame
        """
        group_by_columns = aggregation.get('group_by', [])
        metrics = aggregation.get('metrics', [])
        
        # Validate group by columns exist
        available_columns = df.column_names
        valid_group_by = [col for col in group_by_columns if col in available_columns]
        
        if not valid_group_by:
            logger.warning(f"No valid group by columns found: {group_by_columns}")
            return df.limit(0)  # Return empty DataFrame
        
        # Start with grouping
        if valid_group_by:
            grouped_df = df.groupby(*valid_group_by)
        else:
            # Global aggregation
            grouped_df = df.groupby()
        
        # Apply metrics
        agg_expressions = []
        for metric in metrics:
            metric_name = metric.get('name')
            metric_type = metric.get('type')
            metric_column = metric.get('column', '*')
            
            if not metric_name or not metric_type:
                continue
            
            # Build aggregation expression based on type
            if metric_type == 'count':
                if metric_column == '*':
                    agg_expr = daft.col('*').count().alias(metric_name)
                else:
                    agg_expr = daft.col(metric_column).count().alias(metric_name)
            elif metric_type == 'count_distinct':
                if metric_column in available_columns:
                    agg_expr = daft.col(metric_column).count_distinct().alias(metric_name)
                else:
                    continue
            elif metric_type == 'sum':
                if metric_column in available_columns:
                    agg_expr = daft.col(metric_column).sum().alias(metric_name)
                else:
                    continue
            elif metric_type == 'avg':
                if metric_column in available_columns:
                    agg_expr = daft.col(metric_column).mean().alias(metric_name)
                else:
                    continue
            elif metric_type == 'min':
                if metric_column in available_columns:
                    agg_expr = daft.col(metric_column).min().alias(metric_name)
                else:
                    continue
            elif metric_type == 'max':
                if metric_column in available_columns:
                    agg_expr = daft.col(metric_column).max().alias(metric_name)
                else:
                    continue
            elif metric_type == 'collect_list':
                if metric_column in available_columns:
                    # Note: collect_list might not be available in all Daft versions
                    # This is a placeholder for list aggregation
                    agg_expr = daft.col(metric_column).count().alias(metric_name)
                else:
                    continue
            else:
                logger.warning(f"Unknown metric type: {metric_type}")
                continue
            
            agg_expressions.append(agg_expr)
        
        if not agg_expressions:
            logger.warning("No valid aggregation expressions found")
            return df.limit(0)
        
        # Apply aggregations
        result_df = grouped_df.agg(*agg_expressions)
        
        # Add Gold layer metadata
        result_df = self._add_gold_metadata(result_df, aggregation)
        
        return result_df
    
    def _add_gold_metadata(self, df: daft.DataFrame, aggregation: Dict[str, Any]) -> daft.DataFrame:
        """
        Add Gold layer metadata to the aggregated DataFrame.
        
        Args:
            df: Aggregated DataFrame
            aggregation: Aggregation configuration
            
        Returns:
            DataFrame with Gold layer metadata
        """
        current_time = datetime.now(timezone.utc).isoformat()
        
        df = df.with_column('_gold_processing_time', daft.lit(current_time))
        df = df.with_column('_gold_aggregation_name', daft.lit(aggregation.get('name', 'unknown')))
        df = df.with_column('_gold_aggregation_window', daft.lit(self.aggregation_window))
        df = df.with_column('_gold_processor_version', daft.lit('1.0.0'))
        
        return df
    
    def _write_gold_data(self, df: daft.DataFrame, table_name: str):
        """
        Write DataFrame to Gold layer Iceberg table.
        
        Args:
            df: DataFrame to write
            table_name: Target table name
        """
        try:
            # Get or create Gold table
            gold_table = self._get_or_create_gold_table(table_name, df)
            
            # Write data (using overwrite mode for aggregated data)
            df.write_iceberg(table=gold_table, mode="overwrite")
            
            logger.info(f"Successfully wrote data to Gold table: {table_name}")
            
        except Exception as e:
            logger.error(f"Failed to write to Gold table {table_name}: {e}")
            raise
    
    def _get_or_create_gold_table(self, table_name: str, df: daft.DataFrame) -> Table:
        """
        Get existing Gold table or create new one.
        
        Args:
            table_name: Table name
            df: Sample DataFrame for schema inference
            
        Returns:
            Iceberg table
        """
        full_table_name = f"{self.database_name}.{table_name}"
        
        try:
            # Try to load existing table
            table = self.catalog.load_table(full_table_name)
            logger.info(f"Loaded existing Gold table: {full_table_name}")
            return table
        except Exception:
            # Create new table
            logger.info(f"Creating new Gold table: {full_table_name}")
            
            # Create schema from DataFrame
            from pyiceberg.schema import Schema
            from pyiceberg.types import StringType, LongType, DoubleType, NestedField
            
            fields = []
            for i, col_name in enumerate(df.column_names):
                # Infer type based on column name patterns
                if any(keyword in col_name.lower() for keyword in ['count', 'total', 'sum']):
                    field_type = LongType()
                elif any(keyword in col_name.lower() for keyword in ['avg', 'mean', 'rate', 'percent']):
                    field_type = DoubleType()
                else:
                    field_type = StringType()
                
                fields.append(NestedField(i + 1, col_name, field_type, required=False))
            
            schema = Schema(*fields)
            
            table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                properties={
                    'write.format.default': 'parquet',
                    'write.parquet.compression-codec': 'zstd',
                    'write.target-file-size-bytes': '268435456'  # 256MB for Gold layer
                }
            )
            
            return table
    
    def create_business_kpis(self, source_tables: List[str], 
                           kpi_definitions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create business KPIs from Silver layer data.
        
        Args:
            source_tables: List of Silver layer source tables
            kpi_definitions: List of KPI definitions
            
        Returns:
            Dictionary containing KPI results
        """
        logger.info("Creating business KPIs")
        
        kpi_results = {}
        
        try:
            # Read Silver layer data
            combined_df = None
            for source_table in source_tables:
                df = self._read_silver_data(source_table, datetime.now(timezone.utc))
                if df.count_rows() > 0:
                    if combined_df is None:
                        combined_df = df
                    else:
                        combined_df = combined_df.concat(df)
            
            if combined_df is None:
                return {'status': 'no_data', 'kpis': {}}
            
            # Calculate each KPI
            for kpi_def in kpi_definitions:
                kpi_name = kpi_def.get('name')
                kpi_type = kpi_def.get('type')
                
                try:
                    if kpi_type == 'conversion_rate':
                        kpi_value = self._calculate_conversion_rate(combined_df, kpi_def)
                    elif kpi_type == 'retention_rate':
                        kpi_value = self._calculate_retention_rate(combined_df, kpi_def)
                    elif kpi_type == 'average_session_duration':
                        kpi_value = self._calculate_avg_session_duration(combined_df, kpi_def)
                    elif kpi_type == 'revenue_per_user':
                        kpi_value = self._calculate_revenue_per_user(combined_df, kpi_def)
                    else:
                        logger.warning(f"Unknown KPI type: {kpi_type}")
                        continue
                    
                    kpi_results[kpi_name] = kpi_value
                    logger.info(f"Calculated KPI {kpi_name}: {kpi_value}")
                    
                except Exception as e:
                    logger.error(f"Failed to calculate KPI {kpi_name}: {e}")
                    kpi_results[kpi_name] = None
            
            return {'status': 'success', 'kpis': kpi_results}
            
        except Exception as e:
            logger.error(f"Failed to create business KPIs: {e}")
            return {'status': 'error', 'error': str(e), 'kpis': {}}
    
    def _calculate_conversion_rate(self, df: daft.DataFrame, kpi_def: Dict[str, Any]) -> float:
        """Calculate conversion rate KPI."""
        # Simplified conversion rate calculation
        total_users = df.select('user_id').distinct().count_rows()
        converted_users = df.filter(
            daft.col('event_type') == kpi_def.get('conversion_event', 'purchase')
        ).select('user_id').distinct().count_rows()
        
        return (converted_users / total_users) * 100 if total_users > 0 else 0.0
    
    def _calculate_retention_rate(self, df: daft.DataFrame, kpi_def: Dict[str, Any]) -> float:
        """Calculate retention rate KPI."""
        # Simplified retention rate calculation
        # This would need more sophisticated logic in practice
        return 75.0  # Placeholder
    
    def _calculate_avg_session_duration(self, df: daft.DataFrame, kpi_def: Dict[str, Any]) -> float:
        """Calculate average session duration KPI."""
        # Simplified session duration calculation
        # This would need session tracking logic in practice
        return 300.0  # Placeholder: 5 minutes
    
    def _calculate_revenue_per_user(self, df: daft.DataFrame, kpi_def: Dict[str, Any]) -> float:
        """Calculate revenue per user KPI."""
        # Simplified revenue per user calculation
        if 'amount_usd' not in df.column_names:
            return 0.0
        
        total_revenue = df.select(daft.col('amount_usd').sum()).collect()[0]['amount_usd']
        total_users = df.select('user_id').distinct().count_rows()
        
        return total_revenue / total_users if total_users > 0 else 0.0
