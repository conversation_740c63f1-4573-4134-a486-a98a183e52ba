"""
ELT Pipeline Management API

FastAPI application for managing ELT pipelines in the Bronze-Silver-Gold
data lakehouse architecture.
"""

import secrets
import json
from typing import Dict, Any, List, Optional

from fastapi import FastAP<PERSON>, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>asi<PERSON>, HTTPBasicCredentials
from pydantic import BaseModel
from starlette.requests import Request
from starlette.responses import JSONResponse

from src import USERNAME, PASSWORD
from src.elt import ELTOrchestrator, create_orchestrator_from_config
from src.utility import logging_util

logger = logging_util.get_logger(__name__)

app = FastAPI(title="ELT Pipeline Manager", description="Manage Bronze-Silver-Gold ELT Pipelines")
security = HTTPBasic()

# Global orchestrator instance
orchestrator: Optional[ELTOrchestrator] = None


class PipelineExecutionRequest(BaseModel):
    """Request model for pipeline execution."""
    pipeline_name: Optional[str] = None
    processing_timestamp: Optional[str] = None
    execute_all: bool = False


class PipelineStatusResponse(BaseModel):
    """Response model for pipeline status."""
    pipeline_name: str
    status: str
    last_execution: Optional[str] = None
    error: Optional[str] = None


def authorize(credentials: HTTPBasicCredentials = Depends(security)):
    """Authorize API requests."""
    correct_username = secrets.compare_digest(credentials.username, USERNAME)
    correct_password = secrets.compare_digest(credentials.password, PASSWORD)
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


@app.on_event("startup")
def on_startup():
    """Initialize ELT orchestrator on startup."""
    global orchestrator
    try:
        # Load ELT configuration
        config_path = "config/elt_pipeline_config.json"
        orchestrator = create_orchestrator_from_config(config_path)
        
        # Start scheduler
        orchestrator.start_scheduler()
        
        logger.info("ELT Pipeline Manager started successfully")
    except Exception as e:
        logger.error(f"Failed to start ELT Pipeline Manager: {e}")
        # Don't fail startup, allow manual configuration


@app.on_event("shutdown")
def on_shutdown():
    """Cleanup on shutdown."""
    global orchestrator
    if orchestrator:
        orchestrator.shutdown()
        logger.info("ELT Pipeline Manager shutdown complete")


@app.get('/health', include_in_schema=False)
def health():
    """Health check endpoint."""
    return {
        'status': 'healthy',
        'orchestrator_initialized': orchestrator is not None,
        'timestamp': '2024-01-15T10:30:00Z'
    }


@app.post('/elt/execute-pipeline', dependencies=[Depends(authorize)])
def execute_pipeline(request: PipelineExecutionRequest, background_tasks: BackgroundTasks):
    """
    Execute a specific ELT pipeline or all pipelines.
    
    Args:
        request: Pipeline execution request
        background_tasks: FastAPI background tasks
        
    Returns:
        Execution status
    """
    if not orchestrator:
        raise HTTPException(status_code=500, detail="ELT orchestrator not initialized")
    
    try:
        if request.execute_all:
            # Execute all pipelines asynchronously
            result_refs = orchestrator.execute_all_pipelines_async(request.processing_timestamp)
            
            # Add background task to monitor completion
            background_tasks.add_task(monitor_pipeline_execution, result_refs)
            
            return {
                'status': 'started',
                'message': f'Started execution of {len(result_refs)} pipelines',
                'pipelines_count': len(result_refs)
            }
        
        elif request.pipeline_name:
            # Execute specific pipeline
            result_ref = orchestrator.execute_pipeline_async(
                request.pipeline_name, 
                request.processing_timestamp
            )
            
            # Add background task to monitor completion
            background_tasks.add_task(monitor_single_pipeline_execution, result_ref, request.pipeline_name)
            
            return {
                'status': 'started',
                'message': f'Started execution of pipeline: {request.pipeline_name}',
                'pipeline_name': request.pipeline_name
            }
        
        else:
            raise HTTPException(status_code=400, detail="Must specify pipeline_name or execute_all=true")
    
    except Exception as e:
        logger.error(f"Failed to execute pipeline: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post('/elt/execute-pipeline-sync', dependencies=[Depends(authorize)])
def execute_pipeline_sync(request: PipelineExecutionRequest):
    """
    Execute a pipeline synchronously and wait for completion.
    
    Args:
        request: Pipeline execution request
        
    Returns:
        Execution result
    """
    if not orchestrator:
        raise HTTPException(status_code=500, detail="ELT orchestrator not initialized")
    
    if not request.pipeline_name:
        raise HTTPException(status_code=400, detail="pipeline_name is required for sync execution")
    
    try:
        result = orchestrator.execute_pipeline_sync(
            request.pipeline_name,
            request.processing_timestamp
        )
        
        return {
            'status': 'completed',
            'pipeline_name': request.pipeline_name,
            'result': result
        }
    
    except Exception as e:
        logger.error(f"Failed to execute pipeline synchronously: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get('/elt/pipeline-status/{pipeline_name}', dependencies=[Depends(authorize)])
def get_pipeline_status(pipeline_name: str):
    """
    Get status of a specific pipeline.
    
    Args:
        pipeline_name: Name of the pipeline
        
    Returns:
        Pipeline status
    """
    if not orchestrator:
        raise HTTPException(status_code=500, detail="ELT orchestrator not initialized")
    
    try:
        status = orchestrator.get_pipeline_status(pipeline_name)
        return status
    
    except Exception as e:
        logger.error(f"Failed to get pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get('/elt/pipeline-status', dependencies=[Depends(authorize)])
def get_all_pipeline_status():
    """
    Get status of all pipelines.
    
    Returns:
        Dictionary of all pipeline statuses
    """
    if not orchestrator:
        raise HTTPException(status_code=500, detail="ELT orchestrator not initialized")
    
    try:
        status_dict = orchestrator.get_all_pipeline_status()
        return {
            'pipelines': status_dict,
            'total_pipelines': len(status_dict)
        }
    
    except Exception as e:
        logger.error(f"Failed to get all pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post('/elt/start-scheduler', dependencies=[Depends(authorize)])
def start_scheduler():
    """Start the ELT pipeline scheduler."""
    if not orchestrator:
        raise HTTPException(status_code=500, detail="ELT orchestrator not initialized")
    
    try:
        orchestrator.start_scheduler()
        return {'status': 'started', 'message': 'ELT scheduler started successfully'}
    
    except Exception as e:
        logger.error(f"Failed to start scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post('/elt/stop-scheduler', dependencies=[Depends(authorize)])
def stop_scheduler():
    """Stop the ELT pipeline scheduler."""
    if not orchestrator:
        raise HTTPException(status_code=500, detail="ELT orchestrator not initialized")
    
    try:
        orchestrator.stop_scheduler()
        return {'status': 'stopped', 'message': 'ELT scheduler stopped successfully'}
    
    except Exception as e:
        logger.error(f"Failed to stop scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post('/elt/reload-config', dependencies=[Depends(authorize)])
def reload_config():
    """Reload ELT configuration and restart orchestrator."""
    global orchestrator
    
    try:
        # Shutdown existing orchestrator
        if orchestrator:
            orchestrator.shutdown()
        
        # Create new orchestrator with updated config
        config_path = "config/elt_pipeline_config.json"
        orchestrator = create_orchestrator_from_config(config_path)
        orchestrator.start_scheduler()
        
        return {'status': 'reloaded', 'message': 'ELT configuration reloaded successfully'}
    
    except Exception as e:
        logger.error(f"Failed to reload config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get('/elt/config', dependencies=[Depends(authorize)])
def get_config():
    """Get current ELT configuration."""
    try:
        config_path = "config/elt_pipeline_config.json"
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        return {
            'config': config,
            'config_path': config_path
        }
    
    except Exception as e:
        logger.error(f"Failed to get config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def monitor_pipeline_execution(result_refs: List):
    """Background task to monitor pipeline execution."""
    try:
        results = orchestrator.wait_for_pipelines(result_refs, timeout=3600)  # 1 hour timeout
        
        success_count = sum(1 for r in results if r.get('status') == 'success')
        error_count = len(results) - success_count
        
        logger.info(f"Pipeline execution completed: {success_count} successful, {error_count} failed")
        
        # In production, you might want to send notifications or update a database
        
    except Exception as e:
        logger.error(f"Error monitoring pipeline execution: {e}")


async def monitor_single_pipeline_execution(result_ref, pipeline_name: str):
    """Background task to monitor single pipeline execution."""
    try:
        import ray
        result = ray.get(result_ref)
        
        if result.get('status') == 'success':
            logger.info(f"Pipeline {pipeline_name} completed successfully")
        else:
            logger.error(f"Pipeline {pipeline_name} failed: {result.get('error', 'Unknown error')}")
        
        # In production, you might want to send notifications or update a database
        
    except Exception as e:
        logger.error(f"Error monitoring pipeline {pipeline_name}: {e}")


@app.exception_handler(Exception)
def generic_exception_handler(request: Request, exc: Exception):
    """Handle generic exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error", "detail": str(exc)},
    )
