import json
import time
from datetime import datetime, timezone
from typing import Dict, Any

from kafka.consumer.fetcher import ConsumerRecord

from src.model.worker_dto import SinkRecordDTO, SinkOperation, SinkOperationType
from src.transformers.transformer import StreamTransformer


class DaftIcebergTransformer(StreamTransformer):
    """
    Transformer for Bronze layer processing that enriches Kafka messages with metadata
    for storage in Iceberg tables using Daft DataFrames.
    
    This transformer:
    1. Parses JSON messages from Kafka
    2. Enriches with Kafka metadata (_kafka_offset, _kafka_partition, etc.)
    3. Adds processing timestamp
    4. Prepares data for Bronze layer storage in Iceberg
    """
    
    def __init__(self, config: dict):
        super().__init__(config)
        self.bronze_table_name = config.get('bronze_table_name', 'bronze_events')
        self.add_processing_metadata = config.get('add_processing_metadata', True)
        
    def transform(self, consumer_record: ConsumerRecord) -> SinkRecordDTO:
        """
        Transforms Kafka ConsumerRecord into enriched SinkRecordDTO for Bronze layer.
        
        Args:
            consumer_record: Kafka consumer record containing the message
            
        Returns:
            SinkRecordDTO: Enriched record ready for Bronze layer storage
            
        Raises:
            ValueError: If message cannot be parsed as JSON
            Exception: For other transformation errors
        """
        try:
            # Parse the JSON message
            if isinstance(consumer_record.value, str):
                message_dict: Dict[str, Any] = json.loads(consumer_record.value)
            elif isinstance(consumer_record.value, dict):
                message_dict = consumer_record.value
            else:
                # Handle bytes or other types
                message_dict = json.loads(str(consumer_record.value))
                
        except (json.JSONDecodeError, TypeError) as e:
            raise ValueError(f"Failed to parse message as JSON: {e}") from e
        
        # Enrich with Kafka metadata for Bronze layer
        enriched_message = self._enrich_with_kafka_metadata(
            message_dict, consumer_record
        )
        
        # Add processing metadata if enabled
        if self.add_processing_metadata:
            enriched_message = self._add_processing_metadata(enriched_message)
        
        # Create sink operation for Bronze layer (always UPSERT for data lake)
        sink_operation = SinkOperation(
            sink_operation_type=SinkOperationType.UPSERT
        )
        
        return SinkRecordDTO(
            key=consumer_record.key,
            message=enriched_message,
            topic=consumer_record.topic,
            offset=consumer_record.offset,
            partition=consumer_record.partition,
            sink_operation=sink_operation
        )
    
    def _enrich_with_kafka_metadata(self, message: Dict[str, Any], 
                                   consumer_record: ConsumerRecord) -> Dict[str, Any]:
        """
        Enriches message with Kafka metadata for Bronze layer lineage tracking.
        
        Args:
            message: Original parsed message
            consumer_record: Kafka consumer record
            
        Returns:
            Dict with added Kafka metadata fields
        """
        enriched = message.copy()
        
        # Add Kafka metadata with underscore prefix to avoid conflicts
        enriched['_kafka_topic'] = consumer_record.topic
        enriched['_kafka_partition'] = consumer_record.partition
        enriched['_kafka_offset'] = consumer_record.offset
        enriched['_kafka_timestamp'] = consumer_record.timestamp
        enriched['_kafka_timestamp_type'] = consumer_record.timestamp_type
        
        # Add key if present
        if consumer_record.key:
            enriched['_kafka_key'] = consumer_record.key
            
        # Add headers if present
        if consumer_record.headers:
            enriched['_kafka_headers'] = dict(consumer_record.headers)
            
        return enriched
    
    def _add_processing_metadata(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adds processing metadata for audit and debugging purposes.
        
        Args:
            message: Message to enrich
            
        Returns:
            Dict with added processing metadata
        """
        enriched = message.copy()
        
        # Add processing timestamp
        processing_time = datetime.now(timezone.utc)
        enriched['_processing_time'] = processing_time.isoformat()
        enriched['_processing_timestamp_ms'] = int(time.time() * 1000)
        
        # Add transformer information
        enriched['_transformer_class'] = self.__class__.__name__
        enriched['_bronze_table'] = self.bronze_table_name
        
        return enriched


class SilverLayerTransformer(StreamTransformer):
    """
    Transformer for Silver layer processing that reads from Bronze Iceberg tables,
    performs data cleaning, validation, and enrichment.
    
    This transformer is used in ELT jobs that process Bronze data into Silver.
    """
    
    def __init__(self, config: dict):
        super().__init__(config)
        self.silver_table_name = config.get('silver_table_name', 'silver_events')
        self.bronze_table_name = config.get('bronze_table_name', 'bronze_events')
        self.data_quality_rules = config.get('data_quality_rules', {})
        
    def transform(self, consumer_record: ConsumerRecord) -> SinkRecordDTO:
        """
        For Silver layer, this method is typically not used directly.
        Instead, Silver processing happens in batch ELT jobs.
        
        This method is kept for interface compatibility.
        """
        # Parse the message (assuming it's already processed Bronze data)
        message_dict = json.loads(consumer_record.value) if isinstance(
            consumer_record.value, str) else consumer_record.value
            
        # Apply Silver layer transformations
        cleaned_message = self._apply_data_quality_rules(message_dict)
        enriched_message = self._enrich_for_silver_layer(cleaned_message)
        
        sink_operation = SinkOperation(
            sink_operation_type=SinkOperationType.UPSERT
        )
        
        return SinkRecordDTO(
            key=consumer_record.key,
            message=enriched_message,
            topic=consumer_record.topic,
            offset=consumer_record.offset,
            partition=consumer_record.partition,
            sink_operation=sink_operation
        )
    
    def _apply_data_quality_rules(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Applies data quality rules for Silver layer processing.
        
        Args:
            message: Bronze layer message
            
        Returns:
            Cleaned message for Silver layer
        """
        cleaned = message.copy()
        
        # Example data quality rules
        for field, rules in self.data_quality_rules.items():
            if field in cleaned:
                value = cleaned[field]
                
                # Null handling
                if rules.get('allow_null', True) is False and value is None:
                    cleaned[f'_{field}_quality_flag'] = 'NULL_VALUE_REJECTED'
                    continue
                    
                # Type validation
                expected_type = rules.get('type')
                if expected_type and not isinstance(value, expected_type):
                    cleaned[f'_{field}_quality_flag'] = f'TYPE_MISMATCH_EXPECTED_{expected_type.__name__}'
                    
                # Range validation for numeric fields
                if isinstance(value, (int, float)):
                    min_val = rules.get('min_value')
                    max_val = rules.get('max_value')
                    if min_val is not None and value < min_val:
                        cleaned[f'_{field}_quality_flag'] = f'BELOW_MIN_{min_val}'
                    elif max_val is not None and value > max_val:
                        cleaned[f'_{field}_quality_flag'] = f'ABOVE_MAX_{max_val}'
        
        return cleaned
    
    def _enrich_for_silver_layer(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enriches message for Silver layer with additional computed fields.
        
        Args:
            message: Cleaned Bronze message
            
        Returns:
            Enriched Silver layer message
        """
        enriched = message.copy()
        
        # Add Silver layer metadata
        enriched['_silver_processing_time'] = datetime.now(timezone.utc).isoformat()
        enriched['_silver_table'] = self.silver_table_name
        enriched['_source_bronze_table'] = self.bronze_table_name
        
        # Add computed fields based on Kafka timestamp
        if '_kafka_timestamp' in enriched and enriched['_kafka_timestamp']:
            kafka_dt = datetime.fromtimestamp(
                enriched['_kafka_timestamp'] / 1000, tz=timezone.utc
            )
            enriched['_event_year'] = kafka_dt.year
            enriched['_event_month'] = kafka_dt.month
            enriched['_event_day'] = kafka_dt.day
            enriched['_event_hour'] = kafka_dt.hour
            enriched['_event_date'] = kafka_dt.date().isoformat()
        
        return enriched


class GoldLayerTransformer(StreamTransformer):
    """
    Transformer for Gold layer processing that creates business-ready aggregated datasets
    from Silver layer data.
    
    This transformer is used in ELT jobs that process Silver data into Gold.
    """
    
    def __init__(self, config: dict):
        super().__init__(config)
        self.gold_table_name = config.get('gold_table_name', 'gold_events')
        self.silver_table_name = config.get('silver_table_name', 'silver_events')
        self.aggregation_rules = config.get('aggregation_rules', {})
        
    def transform(self, consumer_record: ConsumerRecord) -> SinkRecordDTO:
        """
        For Gold layer, this method is typically not used directly.
        Instead, Gold processing happens in batch ELT jobs with complex aggregations.
        
        This method is kept for interface compatibility.
        """
        message_dict = json.loads(consumer_record.value) if isinstance(
            consumer_record.value, str) else consumer_record.value
            
        # Apply Gold layer transformations
        aggregated_message = self._apply_business_logic(message_dict)
        
        sink_operation = SinkOperation(
            sink_operation_type=SinkOperationType.UPSERT
        )
        
        return SinkRecordDTO(
            key=consumer_record.key,
            message=aggregated_message,
            topic=consumer_record.topic,
            offset=consumer_record.offset,
            partition=consumer_record.partition,
            sink_operation=sink_operation
        )
    
    def _apply_business_logic(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Applies business logic transformations for Gold layer.
        
        Args:
            message: Silver layer message
            
        Returns:
            Business-ready Gold layer message
        """
        gold_message = message.copy()
        
        # Add Gold layer metadata
        gold_message['_gold_processing_time'] = datetime.now(timezone.utc).isoformat()
        gold_message['_gold_table'] = self.gold_table_name
        gold_message['_source_silver_table'] = self.silver_table_name
        
        # Apply business rules based on configuration
        for rule_name, rule_config in self.aggregation_rules.items():
            if rule_config.get('enabled', True):
                gold_message[f'_business_{rule_name}'] = self._apply_business_rule(
                    message, rule_config
                )
        
        return gold_message
    
    def _apply_business_rule(self, message: Dict[str, Any], rule_config: Dict[str, Any]) -> Any:
        """
        Applies a specific business rule to the message.
        
        Args:
            message: Input message
            rule_config: Rule configuration
            
        Returns:
            Result of applying the business rule
        """
        rule_type = rule_config.get('type', 'identity')
        
        if rule_type == 'identity':
            return message.get(rule_config.get('field', ''), None)
        elif rule_type == 'categorize':
            # Simple categorization logic
            field_value = message.get(rule_config.get('field', ''), None)
            categories = rule_config.get('categories', {})
            return categories.get(str(field_value), 'UNKNOWN')
        elif rule_type == 'calculate':
            # Simple calculation logic
            fields = rule_config.get('fields', [])
            operation = rule_config.get('operation', 'sum')
            values = [message.get(field, 0) for field in fields if field in message]
            
            if operation == 'sum':
                return sum(values)
            elif operation == 'avg':
                return sum(values) / len(values) if values else 0
            elif operation == 'max':
                return max(values) if values else 0
            elif operation == 'min':
                return min(values) if values else 0
        
        return None
