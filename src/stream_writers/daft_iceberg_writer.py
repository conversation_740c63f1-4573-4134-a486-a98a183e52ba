import logging
import time
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from threading import Lock, Timer

try:
    import daft
    from pyiceberg.catalog import load_catalog
    from pyiceberg.table import Table
except ImportError as e:
    logging.error(f"Required dependencies not installed: {e}")
    logging.error("Please install: pip install getdaft pyiceberg pyarrow")
    raise

from src.model.worker_dto import SinkRecordDTO
from src.stream_writers.stream_writer import StreamWriter


class BatchedDaftIcebergWriter(StreamWriter):
    """
    Batched stream writer that collects records, creates Daft DataFrames,
    and writes to Iceberg tables with configurable batching and timeout.
    
    Features:
    - Configurable batch size and timeout
    - Thread-safe batching
    - Automatic Daft DataFrame creation
    - Iceberg table integration with partitioning
    - Error handling and retry logic
    """
    
    def __init__(self, config: dict):
        super().__init__(config)
        
        # Batching configuration
        self.batch_size = config.get('batch_size', 1000)
        self.batch_timeout_seconds = config.get('batch_timeout_seconds', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay_seconds = config.get('retry_delay_seconds', 5)
        
        # Iceberg configuration
        self.catalog_config = config.get('iceberg_catalog', {})
        self.table_name = config.get('table_name', 'bronze_events')
        self.database_name = config.get('database_name', 'default')
        self.partition_columns = config.get('partition_columns', ['_event_year', '_event_month'])
        
        # Daft configuration
        self.daft_config = config.get('daft_config', {})
        
        # Internal state
        self.current_batch: List[Dict[str, Any]] = []
        self.last_write_time = time.time()
        self.batch_lock = Lock()
        self.timer: Optional[Timer] = None
        
        # Initialize Iceberg catalog and table
        self.catalog = None
        self.table: Optional[Table] = None
        self._initialize_iceberg()
        
        logging.info(f"Initialized BatchedDaftIcebergWriter with batch_size={self.batch_size}, "
                    f"timeout={self.batch_timeout_seconds}s, table={self.database_name}.{self.table_name}")
    
    def _initialize_iceberg(self):
        """Initialize Iceberg catalog and table connection."""
        try:
            # Load Iceberg catalog
            if not self.catalog_config:
                logging.warning("No Iceberg catalog configuration provided, using default")
                self.catalog_config = {
                    'type': 'hive',
                    'uri': 'thrift://localhost:9083',
                    'warehouse': 's3://data-lake/warehouse'
                }
            
            self.catalog = load_catalog(name="default", **self.catalog_config)
            
            # Load or create table
            full_table_name = f"{self.database_name}.{self.table_name}"
            try:
                self.table = self.catalog.load_table(full_table_name)
                logging.info(f"Loaded existing Iceberg table: {full_table_name}")
            except Exception as e:
                logging.warning(f"Table {full_table_name} not found, will be created on first write: {e}")
                
        except Exception as e:
            logging.error(f"Failed to initialize Iceberg catalog: {e}")
            raise
    
    def write(self, streams: List[SinkRecordDTO]) -> None:
        """
        Add records to batch and trigger write if batch is ready.
        
        Args:
            streams: List of SinkRecordDTO records to write
        """
        if not streams:
            return
            
        with self.batch_lock:
            # Convert SinkRecordDTO to dict format for Daft
            for record in streams:
                record_dict = record.message.copy()
                
                # Add additional metadata from SinkRecordDTO
                record_dict['_sink_key'] = record.key
                record_dict['_sink_topic'] = record.topic
                record_dict['_sink_partition'] = record.partition
                record_dict['_sink_offset'] = record.offset
                
                self.current_batch.append(record_dict)
            
            # Check if batch is ready
            batch_ready = (
                len(self.current_batch) >= self.batch_size or
                (time.time() - self.last_write_time) >= self.batch_timeout_seconds
            )
            
            if batch_ready:
                self._flush_batch()
            else:
                # Set timer for timeout-based flush if not already set
                if self.timer is None or not self.timer.is_alive():
                    remaining_time = self.batch_timeout_seconds - (time.time() - self.last_write_time)
                    if remaining_time > 0:
                        self.timer = Timer(remaining_time, self._timeout_flush)
                        self.timer.start()
    
    def _timeout_flush(self):
        """Flush batch due to timeout."""
        with self.batch_lock:
            if self.current_batch:
                logging.info(f"Flushing batch due to timeout: {len(self.current_batch)} records")
                self._flush_batch()
    
    def _flush_batch(self):
        """
        Flush current batch to Iceberg using Daft DataFrame.
        This method should be called while holding the batch_lock.
        """
        if not self.current_batch:
            return
            
        batch_to_write = self.current_batch.copy()
        batch_size = len(batch_to_write)
        
        logging.info(f"Flushing batch of {batch_size} records to {self.database_name}.{self.table_name}")
        
        try:
            # Create Daft DataFrame from batch
            df = daft.from_pylist(batch_to_write)
            
            # Apply Daft transformations for partitioning and data preparation
            df = self._prepare_dataframe_for_iceberg(df)
            
            # Write to Iceberg
            self._write_dataframe_to_iceberg(df)
            
            # Clear batch and update timestamp
            self.current_batch.clear()
            self.last_write_time = time.time()
            
            # Cancel timeout timer if active
            if self.timer and self.timer.is_alive():
                self.timer.cancel()
                self.timer = None
                
            logging.info(f"Successfully wrote {batch_size} records to Iceberg")
            
        except Exception as e:
            logging.error(f"Failed to flush batch: {e}")
            # In production, you might want to implement retry logic here
            # For now, we'll clear the batch to prevent infinite accumulation
            self.current_batch.clear()
            raise
    
    def _prepare_dataframe_for_iceberg(self, df: daft.DataFrame) -> daft.DataFrame:
        """
        Prepare Daft DataFrame for Iceberg write with partitioning columns.
        
        Args:
            df: Input Daft DataFrame
            
        Returns:
            Prepared DataFrame with partitioning columns
        """
        try:
            # Add partitioning columns if they don't exist
            if '_kafka_timestamp' in df.column_names:
                # Convert Kafka timestamp to datetime and extract partition columns
                df = df.with_column(
                    '_kafka_datetime',
                    daft.col('_kafka_timestamp').cast(daft.DataType.timestamp('ms'))
                )
                
                if '_event_year' not in df.column_names:
                    df = df.with_column('_event_year', daft.col('_kafka_datetime').dt.year())
                    
                if '_event_month' not in df.column_names:
                    df = df.with_column('_event_month', daft.col('_kafka_datetime').dt.month())
                    
                if '_event_day' not in df.column_names:
                    df = df.with_column('_event_day', daft.col('_kafka_datetime').dt.day())
            
            # Add write timestamp
            current_time = datetime.now(timezone.utc).isoformat()
            df = df.with_column('_write_timestamp', daft.lit(current_time))
            
            # Filter out any null records if needed
            # df = df.filter(daft.col('_kafka_offset').is_not_null())
            
            # Select and order columns for consistent schema
            # This helps with Iceberg schema evolution
            ordered_columns = self._get_ordered_columns(df.column_names)
            if ordered_columns:
                df = df.select(*ordered_columns)
            
            return df
            
        except Exception as e:
            logging.error(f"Error preparing DataFrame for Iceberg: {e}")
            raise
    
    def _get_ordered_columns(self, available_columns: List[str]) -> List[str]:
        """
        Get ordered list of columns for consistent Iceberg schema.
        
        Args:
            available_columns: List of available column names
            
        Returns:
            Ordered list of column names
        """
        # Define preferred column order
        priority_columns = [
            '_kafka_key', '_kafka_topic', '_kafka_partition', '_kafka_offset',
            '_kafka_timestamp', '_kafka_datetime', '_processing_time',
            '_event_year', '_event_month', '_event_day',
            '_write_timestamp'
        ]
        
        ordered = []
        remaining = set(available_columns)
        
        # Add priority columns first
        for col in priority_columns:
            if col in remaining:
                ordered.append(col)
                remaining.remove(col)
        
        # Add remaining columns in alphabetical order
        ordered.extend(sorted(remaining))
        
        return ordered
    
    def _write_dataframe_to_iceberg(self, df: daft.DataFrame):
        """
        Write Daft DataFrame to Iceberg table.
        
        Args:
            df: Prepared Daft DataFrame
        """
        retries = 0
        while retries <= self.max_retries:
            try:
                if self.table is None:
                    # Create table if it doesn't exist
                    self._create_iceberg_table_if_not_exists(df)
                
                # Write DataFrame to Iceberg
                # Note: This is a simplified example. In practice, you might need
                # to convert the Daft DataFrame to PyArrow format first
                df.write_iceberg(
                    table=self.table,
                    mode="append"
                )
                
                logging.info(f"Successfully wrote DataFrame to Iceberg table {self.table_name}")
                return
                
            except Exception as e:
                retries += 1
                if retries > self.max_retries:
                    logging.error(f"Failed to write to Iceberg after {self.max_retries} retries: {e}")
                    raise
                else:
                    logging.warning(f"Write attempt {retries} failed, retrying in {self.retry_delay_seconds}s: {e}")
                    time.sleep(self.retry_delay_seconds)
    
    def _create_iceberg_table_if_not_exists(self, df: daft.DataFrame):
        """
        Create Iceberg table if it doesn't exist based on DataFrame schema.
        
        Args:
            df: DataFrame to infer schema from
        """
        try:
            full_table_name = f"{self.database_name}.{self.table_name}"
            
            # Convert Daft schema to Iceberg schema
            # This is a simplified example - you might need more sophisticated schema mapping
            schema = self._daft_to_iceberg_schema(df.schema())
            
            # Create table with partitioning
            partition_spec = []
            for col in self.partition_columns:
                if col in df.column_names:
                    partition_spec.append(col)
            
            self.table = self.catalog.create_table(
                identifier=full_table_name,
                schema=schema,
                partition_spec=partition_spec if partition_spec else None
            )
            
            logging.info(f"Created new Iceberg table: {full_table_name}")
            
        except Exception as e:
            logging.error(f"Failed to create Iceberg table: {e}")
            raise
    
    def _daft_to_iceberg_schema(self, daft_schema) -> Any:
        """
        Convert Daft schema to Iceberg schema.
        
        Args:
            daft_schema: Daft DataFrame schema
            
        Returns:
            Iceberg schema
        """
        # This is a placeholder - you'll need to implement proper schema conversion
        # based on your specific requirements and the actual Daft/Iceberg APIs
        from pyiceberg.schema import Schema
        from pyiceberg.types import (
            StringType, IntegerType, LongType, DoubleType, 
            BooleanType, TimestampType, NestedField
        )
        
        fields = []
        field_id = 1
        
        # Add some common fields - you'll need to adapt this based on your data
        fields.append(NestedField(field_id, "_kafka_key", StringType(), required=False))
        field_id += 1
        fields.append(NestedField(field_id, "_kafka_topic", StringType(), required=True))
        field_id += 1
        fields.append(NestedField(field_id, "_kafka_partition", IntegerType(), required=True))
        field_id += 1
        fields.append(NestedField(field_id, "_kafka_offset", LongType(), required=True))
        field_id += 1
        fields.append(NestedField(field_id, "_kafka_timestamp", LongType(), required=False))
        field_id += 1
        fields.append(NestedField(field_id, "_processing_time", StringType(), required=False))
        field_id += 1
        fields.append(NestedField(field_id, "_event_year", IntegerType(), required=False))
        field_id += 1
        fields.append(NestedField(field_id, "_event_month", IntegerType(), required=False))
        field_id += 1
        fields.append(NestedField(field_id, "_write_timestamp", StringType(), required=False))
        
        return Schema(*fields)
    
    def close(self) -> None:
        """
        Close the writer and flush any remaining records.
        """
        logging.info("Closing BatchedDaftIcebergWriter")
        
        with self.batch_lock:
            if self.current_batch:
                logging.info(f"Flushing final batch of {len(self.current_batch)} records")
                self._flush_batch()
            
            if self.timer and self.timer.is_alive():
                self.timer.cancel()
        
        logging.info("BatchedDaftIcebergWriter closed successfully")
